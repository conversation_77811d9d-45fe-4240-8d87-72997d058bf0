<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShieldsGF Portal - Documentation Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/docs.css" rel="stylesheet">
    <style>
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 0.5rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="docs-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="logo-section">
                        <h1 class="docs-title">
                            <i class="fas fa-shield-alt me-2"></i>
                            ShieldsGF Portal
                            <span class="docs-subtitle">Documentation Debug</span>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar Navigation -->
            <div class="col-lg-3">
                <nav class="docs-sidebar">
                    <div class="sidebar-header">
                        <h5>Navigation</h5>
                    </div>
                    <div class="sidebar-content" id="sidebarContent">
                        <!-- Navigation will be populated by JavaScript -->
                    </div>
                </nav>
            </div>

            <!-- Main Content Area -->
            <div class="col-lg-9">
                <main class="docs-main">
                    <!-- Debug Info -->
                    <div class="debug-info">
                        <h4>Debug Information</h4>
                        <div id="debugInfo">Loading...</div>
                    </div>

                    <!-- Content Area -->
                    <div class="docs-content" id="docsContent">
                        <h2>Loading content...</h2>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="assets/js/data.js"></script>
    <script>
        $(document).ready(() => {
            console.log('Document ready');
            
            // Debug information
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML = `
                <p><strong>docsData available:</strong> ${typeof docsData !== 'undefined'}</p>
                <p><strong>navigationStructure available:</strong> ${typeof navigationStructure !== 'undefined'}</p>
                <p><strong>jQuery available:</strong> ${typeof $ !== 'undefined'}</p>
            `;
            
            if (typeof docsData !== 'undefined') {
                debugInfo.innerHTML += `
                    <p><strong>Available sections:</strong> ${Object.keys(docsData).join(', ')}</p>
                `;
                
                // Render navigation
                const $sidebar = $('#sidebarContent');
                $sidebar.empty();

                navigationStructure.forEach(section => {
                    const $section = $(`
                        <div class="nav-section">
                            <div class="nav-section-title">
                                <i class="${section.icon}"></i>
                                ${section.title}
                            </div>
                            <ul class="nav-items">
                                ${section.items.map(item => `
                                    <li class="nav-item">
                                        <a href="#" class="nav-link" data-section="${item.id}">
                                            <i class="${item.icon}"></i>
                                            ${item.title}
                                        </a>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                    `);
                    
                    $sidebar.append($section);
                });
                
                // Handle navigation clicks
                $(document).on('click', '.nav-link', (e) => {
                    e.preventDefault();
                    const sectionId = $(e.currentTarget).data('section');
                    console.log('Clicked section:', sectionId);
                    
                    if (docsData[sectionId]) {
                        const section = docsData[sectionId];
                        $('#docsContent').html(section.content);
                        
                        // Update active state
                        $('.nav-link').removeClass('active');
                        $(e.currentTarget).addClass('active');
                        
                        debugInfo.innerHTML += `<p><strong>Loaded section:</strong> ${section.title}</p>`;
                    } else {
                        debugInfo.innerHTML += `<p style="color: red;"><strong>Section not found:</strong> ${sectionId}</p>`;
                    }
                });
                
                // Load overview by default
                if (docsData['overview']) {
                    $('#docsContent').html(docsData['overview'].content);
                    $('.nav-link[data-section="overview"]').addClass('active');
                    debugInfo.innerHTML += `<p><strong>Default section loaded:</strong> Overview</p>`;
                }
            } else {
                debugInfo.innerHTML += `<p style="color: red;"><strong>Error:</strong> docsData not available</p>`;
            }
        });
    </script>
</body>
</html>
