<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Test</title>
</head>
<body>
    <h1>Testing Documentation Data</h1>
    <div id="output"></div>

    <script src="assets/js/data.js"></script>
    <script>
        console.log('Testing data structure...');
        console.log('docsData:', docsData);
        console.log('navigationStructure:', navigationStructure);
        
        const output = document.getElementById('output');
        
        // Display available sections
        output.innerHTML = '<h2>Available Sections:</h2>';
        Object.keys(docsData).forEach(key => {
            const section = docsData[key];
            output.innerHTML += `
                <div style="border: 1px solid #ccc; margin: 10px; padding: 10px;">
                    <h3>${section.title}</h3>
                    <p><strong>ID:</strong> ${section.id}</p>
                    <p><strong>Description:</strong> ${section.description}</p>
                    <p><strong>Content Length:</strong> ${section.content.length} characters</p>
                </div>
            `;
        });
        
        // Display navigation structure
        output.innerHTML += '<h2>Navigation Structure:</h2>';
        navigationStructure.forEach(nav => {
            output.innerHTML += `
                <div style="border: 1px solid #00f; margin: 10px; padding: 10px;">
                    <h3>${nav.title}</h3>
                    <ul>
                        ${nav.items.map(item => `<li>${item.title} (${item.id})</li>`).join('')}
                    </ul>
                </div>
            `;
        });
    </script>
</body>
</html>
