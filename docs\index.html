<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShieldsGF Portal - Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="assets/css/docs.css" rel="stylesheet">
</head>
<body class="loading">
    <!-- Loading Screen -->
    <div id="loader" class="loader-overlay">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <h3>Loading Documentation...</h3>
        </div>
    </div>

    <!-- Custom Cursor -->
    <div class="cursor-holder d-none d-xl-block">
        <div class="cursor"></div>
        <div class="cursor"></div>
    </div>

    <!-- Header -->
    <header class="docs-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="logo-section">
                        <h1 class="docs-title">
                            <i class="fas fa-shield-alt me-2"></i>
                            ShieldsGF Portal
                            <span class="docs-subtitle">Documentation</span>
                        </h1>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="header-actions">
                        <div class="search-container">
                            <input type="text" id="searchInput" class="search-input" placeholder="Search documentation...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <button class="theme-toggle" id="themeToggle">
                            <i class="fas fa-moon"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="docs-container">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Navigation -->
                <div class="col-lg-3 col-xl-2">
                    <nav class="docs-sidebar">
                        <div class="sidebar-header">
                            <h5>Table of Contents</h5>
                            <button class="sidebar-toggle d-lg-none" id="sidebarToggle">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                        <div class="sidebar-content" id="sidebarContent">
                            <!-- Navigation will be populated by JavaScript -->
                        </div>
                    </nav>
                </div>

                <!-- Main Content Area -->
                <div class="col-lg-9 col-xl-10">
                    <main class="docs-main">
                        <!-- Breadcrumb -->
                        <nav aria-label="breadcrumb" class="docs-breadcrumb">
                            <ol class="breadcrumb" id="breadcrumb">
                                <li class="breadcrumb-item"><a href="#overview">Documentation</a></li>
                            </ol>
                        </nav>

                        <!-- Content Area -->
                        <div class="docs-content" id="docsContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>

                        <!-- Navigation Footer -->
                        <div class="docs-navigation-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="nav-previous" id="navPrevious">
                                        <!-- Previous link will be populated by JavaScript -->
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <div class="nav-next" id="navNext">
                                        <!-- Next link will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Search Results Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Search Results</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="searchResults">
                        <!-- Search results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confetti Canvas -->
    <canvas id="confettiBox"></canvas>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tsparticles-confetti@2.10.1/tsparticles.confetti.bundle.min.js"></script>
    <script src="assets/js/data.js"></script>
    <script src="assets/js/docs.js"></script>
</body>
</html>
