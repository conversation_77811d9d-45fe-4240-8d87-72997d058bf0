<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShieldsGF Portal - Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="assets/css/docs.css" rel="stylesheet">
</head>
<body class="loading">
    <!-- Loading Screen -->
    <div id="loader" class="loader-overlay">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <h3>Loading Documentation...</h3>
        </div>
    </div>

    <!-- Custom Cursor -->
    <div class="cursor-holder d-none d-xl-block">
        <div class="cursor"></div>
        <div class="cursor"></div>
    </div>

    <!-- Header -->
    <header class="docs-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="logo-section">
                        <h1 class="docs-title">
                            <i class="fas fa-shield-alt me-2"></i>
                            ShieldsGF Portal
                            <span class="docs-subtitle">Documentation</span>
                        </h1>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="header-actions">
                        <div class="search-container">
                            <input type="text" id="searchInput" class="search-input" placeholder="Search documentation...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <button class="theme-toggle" id="themeToggle">
                            <i class="fas fa-moon"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="docs-container">
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar Navigation -->
                <div class="col-lg-3 col-xl-2">
                    <nav class="docs-sidebar">
                        <div class="sidebar-header">
                            <h5>Table of Contents</h5>
                            <button class="sidebar-toggle d-lg-none" id="sidebarToggle">
                                <i class="fas fa-bars"></i>
                            </button>
                        </div>
                        <div class="sidebar-content" id="sidebarContent">
                            <!-- Navigation will be populated by JavaScript -->
                        </div>
                    </nav>
                </div>

                <!-- Main Content Area -->
                <div class="col-lg-9 col-xl-10">
                    <main class="docs-main">
                        <!-- Breadcrumb -->
                        <nav aria-label="breadcrumb" class="docs-breadcrumb">
                            <ol class="breadcrumb" id="breadcrumb">
                                <li class="breadcrumb-item"><a href="#overview">Documentation</a></li>
                            </ol>
                        </nav>

                        <!-- Content Area -->
                        <div class="docs-content" id="docsContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>

                        <!-- Navigation Footer -->
                        <div class="docs-navigation-footer">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="nav-previous" id="navPrevious">
                                        <!-- Previous link will be populated by JavaScript -->
                                    </div>
                                </div>
                                <div class="col-md-6 text-end">
                                    <div class="nav-next" id="navNext">
                                        <!-- Next link will be populated by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </main>
                </div>
            </div>
        </div>
    </div>

    <!-- Back to Top Button -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- Search Results Modal -->
    <div class="modal fade" id="searchModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Search Results</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="searchResults">
                        <!-- Search results will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Confetti Canvas -->
    <canvas id="confettiBox"></canvas>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/tsparticles-confetti@2.10.1/tsparticles.confetti.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        // Simple documentation system that fetches from structure.md
        $(document).ready(() => {
            console.log('Loading documentation from structure.md...');

            // Load saved theme
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                $('body').attr('data-theme', 'dark');
                $('#themeToggle i').removeClass('fa-moon').addClass('fa-sun');
            }

            // Setup event listeners
            setupEventListeners();

            // Load markdown content
            loadMarkdownContent();
        });

        async function loadMarkdownContent() {
            try {
                const response = await fetch('structure.md');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const markdownText = await response.text();
                console.log('Markdown loaded, length:', markdownText.length);

                // Parse markdown to HTML
                const htmlContent = marked.parse(markdownText);

                // Process the content
                processContent(htmlContent, markdownText);

                hideLoader();

            } catch (error) {
                console.error('Error loading markdown:', error);
                $('#docsContent').html(`
                    <div class="alert alert-danger">
                        <h4>Error Loading Documentation</h4>
                        <p>Could not load the documentation file. Please make sure you're serving this from a web server.</p>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <hr>
                        <p><strong>To fix this:</strong></p>
                        <ul>
                            <li>Use a local web server: <code>python -m http.server 8000</code></li>
                            <li>Or use Node.js: <code>npx serve .</code></li>
                            <li>Then visit: <code>http://localhost:8000</code></li>
                        </ul>
                    </div>
                `);
                hideLoader();
            }
        }

        function processContent(htmlContent, markdownText) {
            // Extract sections from markdown
            const sections = extractSections(markdownText);
            console.log('Extracted sections:', sections.length);

            // Render navigation
            renderNavigation(sections);

            // Display full content initially
            $('#docsContent').html(htmlContent);

            // Update breadcrumb
            $('#breadcrumb').html(`
                <li class="breadcrumb-item active">Complete Documentation</li>
            `);

            // Highlight code blocks
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();
            }

            // Setup section navigation
            setupSectionNavigation(sections, htmlContent);
        }

        function extractSections(markdownText) {
            const sections = [];
            const lines = markdownText.split('\n');
            let currentSection = null;

            lines.forEach((line, index) => {
                // Match headers (# ## ### etc.)
                const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
                if (headerMatch) {
                    const level = headerMatch[1].length;
                    const title = headerMatch[2];
                    const id = title.toLowerCase()
                        .replace(/[^\w\s-]/g, '')
                        .replace(/\s+/g, '-')
                        .replace(/-+/g, '-')
                        .trim();

                    const section = {
                        level,
                        title,
                        id,
                        line: index
                    };

                    sections.push(section);

                    if (level <= 2) {
                        currentSection = section;
                    }
                }
            });

            return sections;
        }

        function renderNavigation(sections) {
            const $sidebar = $('#sidebarContent');
            $sidebar.empty();

            // Group sections by level
            const mainSections = sections.filter(s => s.level === 1 || s.level === 2);

            let currentGroup = null;

            mainSections.forEach(section => {
                if (section.level === 1) {
                    // Main section
                    currentGroup = $(`
                        <div class="nav-section">
                            <div class="nav-section-title">
                                <i class="fas fa-book"></i>
                                ${section.title}
                                <i class="fas fa-chevron-down toggle-icon"></i>
                            </div>
                            <ul class="nav-items">
                            </ul>
                        </div>
                    `);
                    $sidebar.append(currentGroup);
                } else if (section.level === 2 && currentGroup) {
                    // Subsection
                    const $item = $(`
                        <li class="nav-item">
                            <a href="#${section.id}" class="nav-link" data-section="${section.id}">
                                <i class="fas fa-file-text"></i>
                                ${section.title}
                            </a>
                        </li>
                    `);
                    currentGroup.find('.nav-items').append($item);
                }
            });

            // If no main sections, create a simple list
            if (mainSections.filter(s => s.level === 1).length === 0) {
                const $section = $(`
                    <div class="nav-section">
                        <div class="nav-section-title">
                            <i class="fas fa-book"></i>
                            Documentation Sections
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <ul class="nav-items">
                        </ul>
                    </div>
                `);

                sections.filter(s => s.level <= 3).forEach(section => {
                    const $item = $(`
                        <li class="nav-item">
                            <a href="#${section.id}" class="nav-link" data-section="${section.id}">
                                <i class="fas fa-file-text"></i>
                                ${section.title}
                            </a>
                        </li>
                    `);
                    $section.find('.nav-items').append($item);
                });

                $sidebar.append($section);
            }
        }

        function setupSectionNavigation(sections, htmlContent) {
            // Add IDs to headers in the HTML content
            let processedHtml = htmlContent;

            sections.forEach(section => {
                const headerRegex = new RegExp(`<h${section.level}>([^<]*${section.title}[^<]*)</h${section.level}>`, 'i');
                processedHtml = processedHtml.replace(headerRegex, `<h${section.level} id="${section.id}">$1</h${section.level}>`);
            });

            $('#docsContent').html(processedHtml);

            // Re-highlight code blocks
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();
            }
        }

        function setupEventListeners() {
            // Navigation clicks
            $(document).on('click', '.nav-link', (e) => {
                e.preventDefault();
                const sectionId = $(e.currentTarget).data('section');
                console.log('Navigation clicked:', sectionId);

                // Scroll to section
                const $target = $(`#${sectionId}`);
                if ($target.length) {
                    $('html, body').animate({
                        scrollTop: $target.offset().top - 100
                    }, 500);

                    // Update active state
                    $('.nav-link').removeClass('active');
                    $(e.currentTarget).addClass('active');

                    // Update URL
                    window.history.pushState({}, '', `#${sectionId}`);
                }
            });

            // Section title clicks (collapse/expand)
            $(document).on('click', '.nav-section-title', (e) => {
                const $section = $(e.currentTarget);
                const $items = $section.next('.nav-items');

                $section.toggleClass('collapsed');
                $items.toggleClass('collapsed');
            });

            // Theme toggle
            $('#themeToggle').on('click', () => {
                toggleTheme();
            });

            // Back to top
            $('#backToTop').on('click', () => {
                $('html, body').animate({ scrollTop: 0 }, 600);
            });

            // Sidebar toggle for mobile
            $('#sidebarToggle').on('click', () => {
                $('.docs-sidebar').toggleClass('show');
            });

            // Scroll effects
            $(window).on('scroll', () => {
                const $backToTop = $('#backToTop');
                if ($(window).scrollTop() > 300) {
                    $backToTop.addClass('visible');
                } else {
                    $backToTop.removeClass('visible');
                }
            });

            // Search functionality
            $('#searchInput').on('input', (e) => {
                const query = e.target.value.toLowerCase();
                if (query.length > 2) {
                    highlightSearchResults(query);
                } else {
                    clearSearchHighlights();
                }
            });
        }

        function highlightSearchResults(query) {
            const $content = $('#docsContent');
            const content = $content.html();

            // Remove previous highlights
            const cleanContent = content.replace(/<mark class="search-highlight">(.*?)<\/mark>/gi, '$1');

            // Add new highlights
            const regex = new RegExp(`(${query})`, 'gi');
            const highlightedContent = cleanContent.replace(regex, '<mark class="search-highlight">$1</mark>');

            $content.html(highlightedContent);
        }

        function clearSearchHighlights() {
            const $content = $('#docsContent');
            const content = $content.html();
            const cleanContent = content.replace(/<mark class="search-highlight">(.*?)<\/mark>/gi, '$1');
            $content.html(cleanContent);
        }

        function toggleTheme() {
            const $body = $('body');
            const $icon = $('#themeToggle i');

            if ($body.attr('data-theme') === 'dark') {
                $body.removeAttr('data-theme');
                $icon.removeClass('fa-sun').addClass('fa-moon');
                localStorage.setItem('theme', 'light');
            } else {
                $body.attr('data-theme', 'dark');
                $icon.removeClass('fa-moon').addClass('fa-sun');
                localStorage.setItem('theme', 'dark');
            }
        }

        function hideLoader() {
            setTimeout(() => {
                $('#loader').addClass('hidden');
                $('body').removeClass('loading');
            }, 1000);
        }

        // Handle initial hash
        $(window).on('load', () => {
            const hash = window.location.hash.substring(1);
            if (hash) {
                setTimeout(() => {
                    const $target = $(`#${hash}`);
                    if ($target.length) {
                        $('html, body').animate({
                            scrollTop: $target.offset().top - 100
                        }, 500);
                        $(`.nav-link[data-section="${hash}"]`).addClass('active');
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>
