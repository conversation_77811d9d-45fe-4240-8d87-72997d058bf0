/* ShieldsGF Portal Documentation Styles */

:root {
    /* Primary Colors - Matching site design */
    --primary-color: #ff6b35;
    --primary-dark: #e55a2b;
    --primary-light: #ff8c66;
    
    /* Secondary Colors */
    --secondary-color: #2c3e50;
    --secondary-light: #34495e;
    --secondary-dark: #1a252f;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-dark: #1a1a1a;
    --bg-dark-secondary: #2d2d2d;
    
    /* Text Colors */
    --text-primary: #2c3e50;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --text-light: #ffffff;
    
    /* Border Colors */
    --border-color: #dee2e6;
    --border-dark: #495057;
    
    /* Shadow */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Border Radius */
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
}

/* Dark Theme Variables */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3d3d3d;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-muted: #888888;
    --border-color: #495057;
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    transition: all var(--transition-normal);
    overflow-x: hidden;
}

body.loading {
    overflow: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Loading Screen */
.loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loader-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    text-align: center;
    color: white;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Cursor */
.cursor-holder {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9998;
}

.cursor {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
    transition: transform var(--transition-fast);
}

.cursor:first-child {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    z-index: 9999;
}

.cursor:last-child {
    width: 32px;
    height: 32px;
    border: 2px solid var(--primary-color);
    opacity: 0.5;
    z-index: 9998;
}

/* Header */
.docs-header {
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
}

.docs-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    display: flex;
    align-items: center;
}

.docs-subtitle {
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--text-secondary);
    margin-left: 0.5rem;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    justify-content: flex-end;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 300px;
}

.search-input {
    width: 100%;
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
}

.search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Main Container */
.docs-container {
    min-height: calc(100vh - 80px);
}

/* Sidebar */
.docs-sidebar {
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    height: calc(100vh - 80px);
    position: sticky;
    top: 80px;
    overflow-y: auto;
    padding: 1.5rem 0;
}

.sidebar-header {
    padding: 0 1.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-header h5 {
    margin: 0;
    color: var(--text-primary);
    font-weight: 600;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
}

.sidebar-content {
    padding: 0 1rem;
}

.nav-section {
    margin-bottom: 1.5rem;
}

.nav-section-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    padding: 0 0.5rem;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.nav-section-title:hover {
    color: var(--primary-dark);
}

.nav-section-title i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.nav-section-title .toggle-icon {
    margin-left: auto;
    transition: transform var(--transition-fast);
}

.nav-section-title.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.nav-items {
    list-style: none;
    padding: 0;
    margin: 0;
    transition: all var(--transition-normal);
}

.nav-items.collapsed {
    max-height: 0;
    overflow: hidden;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
    font-size: 0.875rem;
    position: relative;
}

.nav-link:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: translateX(4px);
}

.nav-link.active {
    background: var(--primary-color);
    color: white;
    font-weight: 500;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: -1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Main Content */
.docs-main {
    padding: 2rem;
    max-width: none;
}

.docs-breadcrumb {
    margin-bottom: 2rem;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--primary-dark);
}

.breadcrumb-item.active {
    color: var(--text-secondary);
}

/* Content Styles */
.docs-content {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content-section {
    margin-bottom: 3rem;
}

.content-section h1 {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 1rem;
    font-size: 2.5rem;
    line-height: 1.2;
}

.content-section h2 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 2rem 0 1rem;
    font-size: 2rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.content-section h3 {
    color: var(--text-primary);
    font-weight: 600;
    margin: 1.5rem 0 1rem;
    font-size: 1.5rem;
}

.content-section h4 {
    color: var(--primary-color);
    font-weight: 600;
    margin: 1rem 0 0.5rem;
    font-size: 1.25rem;
}

.content-section p {
    margin-bottom: 1rem;
    color: var(--text-primary);
    line-height: 1.7;
}

.content-section ul,
.content-section ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.content-section li {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    line-height: 1.6;
}

/* Code Blocks */
pre {
    background: var(--bg-dark) !important;
    border-radius: var(--border-radius);
    padding: 1.5rem !important;
    margin: 1rem 0;
    overflow-x: auto;
    box-shadow: var(--shadow);
}

code {
    background: var(--bg-tertiary);
    color: var(--primary-color);
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: 'Courier New', monospace;
}

pre code {
    background: none;
    color: inherit;
    padding: 0;
}

/* Tables */
.table {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    margin: 1.5rem 0;
}

.table th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    border-color: var(--border-color);
    color: var(--text-primary);
}

.table-striped tbody tr:nth-of-type(odd) {
    background: var(--bg-secondary);
}

/* Cards and Boxes */
.info-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.info-card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.info-card h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.warning-card {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-left: 4px solid #f39c12;
    color: #856404;
}

.success-card {
    background: linear-gradient(135deg, #d4edda, #a8e6cf);
    border-left: 4px solid #28a745;
    color: #155724;
}

.danger-card {
    background: linear-gradient(135deg, #f8d7da, #ffb3ba);
    border-left: 4px solid #dc3545;
    color: #721c24;
}

/* Dashboard Mockups */
.dashboard-mockup {
    background: var(--bg-dark);
    color: #00ff00;
    font-family: 'Courier New', monospace;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin: 1.5rem 0;
    overflow-x: auto;
    box-shadow: var(--shadow);
    border: 1px solid #333;
}

.dashboard-mockup pre {
    background: none !important;
    color: inherit;
    margin: 0;
    padding: 0;
}

/* Navigation Footer */
.docs-navigation-footer {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.nav-previous,
.nav-next {
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    transition: all var(--transition-fast);
}

.nav-previous:hover,
.nav-next:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.nav-previous a,
.nav-next a {
    color: inherit;
    text-decoration: none;
    display: block;
}

.nav-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
}

.nav-title {
    font-weight: 600;
    color: var(--text-primary);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 1000;
    box-shadow: var(--shadow);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* Search Modal */
.search-result-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.search-result-item:hover {
    background: var(--bg-secondary);
}

.search-result-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.search-result-content {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.search-highlight {
    background: var(--primary-color);
    color: white;
    padding: 0.1rem 0.2rem;
    border-radius: 0.2rem;
}

/* Confetti Canvas */
#confettiBox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 9997;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .docs-sidebar {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 300px;
        z-index: 1050;
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-lg);
    }
    
    .docs-sidebar.show {
        left: 0;
    }
    
    .docs-main {
        padding: 1rem;
    }
    
    .content-section h1 {
        font-size: 2rem;
    }
    
    .content-section h2 {
        font-size: 1.5rem;
    }
}

@media (max-width: 767.98px) {
    .header-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .search-container {
        max-width: none;
    }
    
    .docs-title {
        font-size: 1.5rem;
    }
    
    .docs-subtitle {
        display: none;
    }
    
    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }
}

/* Print Styles */
@media print {
    .docs-header,
    .docs-sidebar,
    .docs-navigation-footer,
    .back-to-top,
    .search-container,
    .theme-toggle {
        display: none !important;
    }
    
    .docs-main {
        padding: 0;
        max-width: none;
    }
    
    .docs-container {
        margin: 0;
    }
}
