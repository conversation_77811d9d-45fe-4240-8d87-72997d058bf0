# ShieldsGF Portal Documentation Structure

## Main Documentation Sections

### 1. Getting Started

- Overview
- Installation & Setup
- System Requirements
- Configuration

### 2. User Guide

- Authentication & Login
- Dashboard Overview
- User Roles & Permissions
- Navigation

### 3. Core Features

- Project Management
- Task Management
- Status Hub
- Calendar & PTO Management
- Resource Allocation
- Team Management
- File Management
- Messaging System

### 4. Admin Features

- Admin Dashboard
- User Management
- Role & Permission Management
- System Configuration
- Holiday Management

### 5. API Reference

- Authentication
- Projects API
- Tasks API
- Users API
- Calendar API

### 6. Technical Documentation

- Architecture Overview
- Database Schema
- Livewire Components
- Middleware & Security
- File Structure

### 7. Troubleshooting

- Common Issues
- Error Messages
- FAQ

## File Structure for Documentation

```
docs/
├── index.html                 # Main documentation homepage
├── assets/
│   ├── css/
│   │   └── docs.css          # Documentation styling
│   ├── js/
│   │   ├── docs.js           # Main documentation JavaScript
│   │   └── data.js           # All documentation data
│   └── images/               # Screenshots and diagrams
├── getting-started/
│   ├── overview.html
│   ├── installation.html
│   └── configuration.html
├── user-guide/
│   ├── authentication.html
│   ├── dashboard.html
│   ├── roles-permissions.html
│   └── navigation.html
├── features/
│   ├── project-management.html
│   ├── task-management.html
│   ├── status-hub.html
│   ├── calendar-pto.html
│   ├── resource-allocation.html
│   ├── team-management.html
│   ├── file-management.html
│   └── messaging.html
├── admin/
│   ├── admin-dashboard.html
│   ├── user-management.html
│   ├── role-management.html
│   ├── system-config.html
│   └── holiday-management.html
├── api/
│   ├── authentication.html
│   ├── projects.html
│   ├── tasks.html
│   ├── users.html
│   └── calendar.html
├── technical/
│   ├── architecture.html
│   ├── database.html
│   ├── livewire.html
│   ├── security.html
│   └── file-structure.html
└── troubleshooting/
    ├── common-issues.html
    ├── error-messages.html
    └── faq.html
```

## Data Structure for docs.js

The documentation data will be structured as follows:

```javascript
const docsData = {
  overview: { ... },
  gettingStarted: { ... },
  userGuide: { ... },
  features: { ... },
  admin: { ... },
  api: { ... },
  technical: { ... },
  troubleshooting: { ... }
}
```

Each section will contain:

- title
- description
- content (HTML)
- subsections
- code examples
- screenshots
- related links

## project overview

shield sgf portal is the software for managing the projects and clients efficiently if makes a bridge between clients and Developers to make things smooth and work efficiently

## Roles

there are different roles in the system

- SuperAdmin
  -Admin
  -Team Member
  -Client Member
  -Enterprise Owner

Each Role Elaboration

- SuperAdmin: has all the permissions to manage the system and users
- Admin: has permissions to manage projects, tasks, users.
- Team Member: can manage tasks assigned to them and view project details

Important Note-Some team Member can have admin access level which will give them admin level permissions.

Client Member: can view project details and communicate with team members.

EnterPrise Owner: can view project details and can see all the projects of their clients and communicate with team members.

Data Flow
User->login->system validates all credentials and user role->validation failed->back to login page->validation success->redirect to dashboard based on user role

Different Dashboards-

- SuperAdmin Dashboard
- Admin Dashboard
- Team Member Dashboard
- Client Member Dashboard
- Enterprise Owner Dashboard

What is visible in each dashboard

- SuperAdmin Dashboard: User Management, System Settings, All Projects Overview, All Tasks,PTO Calender, resource allocation and Status Hub.
- Admin Dashboard: User Management, All Projects Overview, All Tasks,PTO Calender and resource allocation

- Team Member Dashboard-Tasks Assigned to them, Projects they are working on, PTO Calender.

- Client Member Dashboard-Their Projects and what currently is going on with the project.

- Enterprise Owner Dashboard-Clients Projects and what currently is going on with the project.

How they are connected with each other

Let's say a new project is added by a admin or a team member who has admin access,

First we have to understand how project is created, for creating a project it is mandatory to have a client.
The clents may or may not have a brand (enterprise) associated with them.

To create a project these fields are mandatory

- AI will add this

Now Team members are connected to the project.

Now tasks are created for the project and assigned to the team members.

Tasks can be of different types
-Urgent
-New
-Feedback
-Completed
-In Progress
-Recently Finished

A task can have due date

Comments in Task-
AI will add this
