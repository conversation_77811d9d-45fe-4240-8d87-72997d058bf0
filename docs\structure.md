# ShieldsGF Portal Documentation Structure

## Main Documentation Sections

### 1. Getting Started

- Overview
- Installation & Setup
- System Requirements
- Configuration

### 2. User Guide

- Authentication & Login
- Dashboard Overview
- User Roles & Permissions
- Navigation

### 3. Core Features

- Project Management
- Task Management
- Status Hub
- Calendar & PTO Management
- Resource Allocation
- Team Management
- File Management
- Messaging System

### 4. Admin Features

- Admin Dashboard
- User Management
- Role & Permission Management
- System Configuration
- Holiday Management

### 5. API Reference

- Authentication
- Projects API
- Tasks API
- Users API
- Calendar API

### 6. Technical Documentation

- Architecture Overview
- Database Schema
- Livewire Components
- Middleware & Security
- File Structure

### 7. Troubleshooting

- Common Issues
- Error Messages
- FAQ

## File Structure for Documentation

```
docs/
├── index.html                 # Main documentation homepage
├── assets/
│   ├── css/
│   │   └── docs.css          # Documentation styling
│   ├── js/
│   │   ├── docs.js           # Main documentation JavaScript
│   │   └── data.js           # All documentation data
│   └── images/               # Screenshots and diagrams
├── getting-started/
│   ├── overview.html
│   ├── installation.html
│   └── configuration.html
├── user-guide/
│   ├── authentication.html
│   ├── dashboard.html
│   ├── roles-permissions.html
│   └── navigation.html
├── features/
│   ├── project-management.html
│   ├── task-management.html
│   ├── status-hub.html
│   ├── calendar-pto.html
│   ├── resource-allocation.html
│   ├── team-management.html
│   ├── file-management.html
│   └── messaging.html
├── admin/
│   ├── admin-dashboard.html
│   ├── user-management.html
│   ├── role-management.html
│   ├── system-config.html
│   └── holiday-management.html
├── api/
│   ├── authentication.html
│   ├── projects.html
│   ├── tasks.html
│   ├── users.html
│   └── calendar.html
├── technical/
│   ├── architecture.html
│   ├── database.html
│   ├── livewire.html
│   ├── security.html
│   └── file-structure.html
└── troubleshooting/
    ├── common-issues.html
    ├── error-messages.html
    └── faq.html
```

## Data Structure for docs.js

The documentation data will be structured as follows:

```javascript
const docsData = {
  overview: { ... },
  gettingStarted: { ... },
  userGuide: { ... },
  features: { ... },
  admin: { ... },
  api: { ... },
  technical: { ... },
  troubleshooting: { ... }
}
```

Each section will contain:

- title
- description
- content (HTML)
- subsections
- code examples
- screenshots
- related links

## project overview

shield sgf portal is the software for managing the projects and clients efficiently if makes a bridge between clients and Developers to make things smooth and work efficiently

## Roles

there are different roles in the system

- SuperAdmin
  -Admin
  -Team Member
  -Client Member
  -Enterprise Owner

Each Role Elaboration

- SuperAdmin: has all the permissions to manage the system and users
- Admin: has permissions to manage projects, tasks, users.
- Team Member: can manage tasks assigned to them and view project details

Important Note-Some team Member can have admin access level which will give them admin level permissions.

Client Member: can view project details and communicate with team members.

EnterPrise Owner: can view project details and can see all the projects of their clients and communicate with team members.

Data Flow
User->login->system validates all credentials and user role->validation failed->back to login page->validation success->redirect to dashboard based on user role

Different Dashboards-

- SuperAdmin Dashboard
- Admin Dashboard
- Team Member Dashboard
- Client Member Dashboard
- Enterprise Owner Dashboard

What is visible in each dashboard

- SuperAdmin Dashboard: User Management, System Settings, All Projects Overview, All Tasks,PTO Calender, resource allocation and Status Hub.
- Admin Dashboard: User Management, All Projects Overview, All Tasks,PTO Calender and resource allocation

- Team Member Dashboard-Tasks Assigned to them, Projects they are working on, PTO Calender.

- Client Member Dashboard-Their Projects and what currently is going on with the project.

- Enterprise Owner Dashboard-Clients Projects and what currently is going on with the project.

How they are connected with each other

Let's say a new project is added by a admin or a team member who has admin access,

First we have to understand how project is created, for creating a project it is mandatory to have a client.
The clents may or may not have a brand (enterprise) associated with them.

To create a project these fields are mandatory

- AI will add this

Now Team members are connected to the project.

Now tasks are created for the project and assigned to the team members.

Tasks can be of different types
-Urgent
-New
-Feedback
-Completed
-In Progress
-Recently Finished

A task can have due date

Comments in Task-

- Rich text editor with formatting options (bold, italic, underline)
- User mentions with @ symbol and dropdown suggestions
- Line breaks supported with Shift+Enter
- Image paste functionality
- Link highlighting and auto-formatting
- Comment threading and replies

## Detailed Feature Documentation

### PTO Calendar System - Complete Workflow

**Admin Capabilities:**

- Add global holidays for all users (company-wide)
- Choose holiday regions (Indian holidays or US holidays)
- Add personal leave for individual team members
- View all team member holidays in calendar view
- Edit and delete holiday events
- Set holiday descriptions and date ranges

**User Capabilities:**

- Add personal holidays/PTO requests
- View team calendar with all holidays
- See project completion dates (displayed in blue)
- View phase completion dates (color-coded by phase index)
- Click on events to see complete details

**Calendar Features:**

- Interactive calendar view with navigation controls
- AJAX-based event addition (no page refresh required)
- Clickable events show detailed information
- Personal holidays display user names (e.g., "John is out")
- Integration with resource allocation calculations
- Holiday events appear at the end of resource allocation display

**Technical Implementation:**

- Holiday model with user relationships
- Global vs personal holiday flags
- Region-based holiday filtering
- Real-time calendar updates via Livewire

### Status Hub - Project Management Center

**Core Functionality:**

- Visual representation of all projects in different phases
- Drag-and-drop project movement between phases
- Real-time status updates and progress tracking
- Project archiving and restoration capabilities

**Project Display Elements:**

- Project boxes with color-coded phases
- Progress indicators showing completion percentage
- Job codes and project names
- Client information display
- Team member flags with individual color codes
- Phase completion status indicators

**Interactive Features:**

- Drag projects between phase columns
- Click project boxes for detailed project view
- Archive/unarchive projects
- Real-time updates without page refresh
- Project filtering and search capabilities

**Phase Management:**

- Projects divided by categories within phases
- Category names displayed instead of phase names
- Phase completion dates tracked
- Project completion determined by highest phase ID target date

### Client Dashboard - Client Portal Features

**Active Projects Section:**

- Current projects in progress with phase indicators
- Progress visualization (e.g., "PHASE 2/5")
- Direct links to project details
- Team member information
- Timeline and milestone tracking

**Voyager Projects Section:**

- Completed projects in maintenance phase
- Access to site analytics
- Billing history and invoicing
- Ongoing support status
- Performance metrics

**Completed Projects Archive:**

- Historical project information
- Final deliverables access
- Project summaries and outcomes
- Client feedback and testimonials

**Communication Tools:**

- Direct messaging with project teams
- File sharing and document access
- Project update notifications
- Meeting scheduling and notes

**Quick Access Features:**

- Project-specific resource links
- Important document downloads
- Contact information for team members
- Support ticket creation

### Resource Allocation System - Comprehensive Guide

**Calculation Methodology:**

- **Standard Work Week:** 32 hours (6+ hours per day)
- **Percentage Calculations:** Based on 40-hour work week for standardization
- **Leave Integration:** Automatically accounts for holidays and PTO
- **Multi-role Support:** Users can have different hour allocations for different roles

**Role-Based Hour Allocation:**

- **Developer Hours:** Pulled from resources table per user
- **Designer Hours:** Pulled from resources table per user
- **Project Manager Hours:** 20-30% allocation during design, code, deploy, and manage phases
- **Customer Success Hours:** Tracked separately in resources table

**Display Format and Features:**

- **Hour Display:** Shows both percentage and actual hours "(allocated/available) hours"
- **Calendar View:** Uses same layout as calendar view (not tabular format)
- **Weekly Tracking:** Displays weekly utilization for each team member
- **Extended Timeline:** Shows weeks beyond current month until all project phases complete
- **Project Transitions:** Tracks how resources move between teams when phases complete

**Advanced Calculations:**

- **Remaining Hours:** Calculates remaining project hours by tracking used hours in each phase
- **Phase Tracking:** Monitors weekly transitions between project phases
- **Utilization Rates:** Can exceed 100% allocation when workload is high
- **Team Transitions:** Tracks resource movement between projects and phases

**Visual Elements:**

- **Reduced Event Height:** Allows more events to display simultaneously
- **Holiday Placement:** Holiday events appear at end of resource allocation percentages
- **No Week Numbers:** Week numbers removed from calendar events for cleaner display
- **Project Names:** Fully visible (not truncated) with clickable details
- **Team Details:** Shows developer, designer, and PM team information

### Task Management System - Complete Workflow

**Task Categories and Display:**

- **Urgent Tasks:** Displayed with star icons for immediate attention
- **New Tasks:** Special indicators for recently created tasks
- **Regular Tasks:** Simple display without special icons
- **Feedback Tasks:** Tasks requiring client or team feedback
- **In Progress:** Currently active tasks
- **Completed:** Finished tasks
- **Recently Finished:** Recently completed tasks (visible only with specific filter)

**Access Control by Role:**

- **Admin/SuperAdmin:** Can view all tasks across all projects
- **Regular Users:** Can only see tasks assigned to them
- **Project-Specific Views:** Tasks filtered by specific project context
- **Client Members:** Can view project-related tasks (limited visibility)

**Task Creation and Management:**

- **Mandatory Fields:** Task name, description, project assignment
- **Optional Fields:** Due date, priority level, estimated duration
- **Assignment:** Can assign to multiple team members
- **Status Workflow:** Defined progression through task states
- **Attachments:** Support for file uploads and attachments

**Rich Text Features:**

- **Formatting Options:** Bold (Ctrl+B), Italic (Ctrl+I), Underline (Ctrl+U)
- **User Mentions:** @ symbol triggers dropdown with user suggestions
- **Line Breaks:** Shift+Enter for line breaks within comments
- **Image Support:** Paste images directly into comments
- **Link Handling:** Automatic link detection and formatting
- **Link Behavior:** Links open in new windows (target='\_blank')

**Comment System:**

- **Threading:** Nested comment replies
- **Mention Replacement:** Selected mentions replace partial typed names
- **Visual Feedback:** Selected text highlighted when formatting applied
- **Toggle Formatting:** Buttons can toggle formatting on/off
- **Timezone Handling:** Timestamps display correctly for user's timezone

### Project Management - Complete Lifecycle

**Project Creation Requirements:**

- **Client Assignment:** Must have existing client (mandatory)
- **Brand Association:** Clients may have associated brands/enterprises
- **Job Code:** Unique identifier for project tracking
- **Project Title:** Descriptive name for the project
- **Timeline:** Project duration and milestones
- **Team Assignment:** Assign team members with specific roles
- **Social Details:** Social media links and information
- **Invoice Schedule:** Billing frequency and terms
- **Harvest Integration:** Link to time tracking system

**Project Phases:**

- **Design Phase:** Initial design and planning
- **Development Phase:** Code implementation and development
- **Deploy Phase:** Testing, deployment, and launch
- **Manage Phase:** Ongoing maintenance and support
- **Voyager Phase:** Long-term maintenance for completed projects

**Phase Management:**

- **Phase Categories:** Projects divided by categories within phases
- **Target Dates:** Each phase has specific target completion dates
- **Progress Tracking:** Visual progress indicators and percentage completion
- **Phase Transitions:** Automatic progression through phases
- **Completion Calculation:** Project completion based on highest phase ID target date

**Project Information Display:**

- **Job Codes:** Unique identifiers (e.g., [SP-003-25])
- **Phase Indicators:** Current phase and total phases (e.g., "PHASE 2/5")
- **Team Flags:** Color-coded team member indicators
- **Client Information:** Associated client and brand details
- **Status Updates:** Real-time project status changes

### Team Management and User Roles

**Role Hierarchy and Permissions:**

**SuperAdmin:**

- Complete system access and control
- User creation, modification, and deletion
- Role and permission management
- System configuration and settings
- Access to all projects and data
- Database management capabilities

**Admin:**

- Project creation and management
- Team member assignment and oversight
- Resource allocation and planning
- Task creation and assignment
- Access to admin dashboard features
- User management (limited)

**Team Members:**

- Task management for assigned tasks
- Project collaboration and communication
- Time tracking and reporting
- File uploads and sharing
- Calendar access for PTO requests
- Limited project visibility

**Team Members with Admin Access Level:**

- Regular team member base permissions
- Additional admin-level permissions
- Project management capabilities
- Enhanced system access
- Resource allocation visibility

**Client Members:**

- Project visibility for their projects only
- Communication with assigned team members
- File access and document downloads
- Project progress tracking
- Limited system interaction

**Enterprise Owners:**

- Access to all client projects under their enterprise
- Multi-client project oversight
- Communication with project teams
- Reporting and analytics access
- Brand-level project management

### File Management System

**Upload Capabilities:**

- **File Size Limit:** 20MB maximum per file
- **Multiple Uploads:** Support for multiple file selection
- **File Types:** Support for documents, images, videos, and archives
- **Project Organization:** Files organized by project structure
- **Version Control:** Track file versions and changes

**Storage Structure:**

- **Project-Based:** Files stored in project-specific directories
- **Message Attachments:** Files linked to project messages
- **Task Attachments:** Files associated with specific tasks
- **User Uploads:** Personal file storage areas

**Access Control:**

- **Role-Based Access:** File visibility based on user roles
- **Project Team Access:** Team members can access project files
- **Client Visibility:** Clients can access relevant project files
- **Admin Override:** Admins have access to all files

**File Operations:**

- **Upload:** Drag-and-drop or browse file selection
- **Download:** Direct file download with access logging
- **Preview:** In-browser preview for supported file types
- **Sharing:** Share files with specific team members or clients
- **Organization:** Folder structure and file categorization

### Messaging System - Communication Hub

**Project Messaging Features:**

- **Thread-Based Conversations:** Organized message threads per project
- **Subject Lines:** Clear message organization with subjects
- **File Attachments:** Support for multiple file attachments (20MB max per file)
- **Email Notifications:** Automatic notifications to relevant team members
- **Reply Functionality:** Nested replies with file attachment support
- **Team Filtering:** Messages filtered by team regions (US team focus)

**Message Creation Process:**

- **Subject Required:** All messages must have descriptive subjects
- **Message Body:** Rich text content with formatting support
- **File Uploads:** Multiple file selection and upload
- **Recipient Selection:** Choose specific team members or send to all
- **Email Integration:** Option to send email notifications along with platform messages

**Message Threading:**

- **Parent-Child Relationships:** Messages linked to original threads
- **Reply Tracking:** Track conversation flow and responses
- **File Inheritance:** Access to files from parent messages
- **Notification Chain:** Continued notifications for thread participants

**Access Control:**

- **Project-Based:** Messages visible to project team members
- **Role Restrictions:** Different visibility based on user roles
- **Client Access:** Clients can participate in relevant project discussions
- **Admin Oversight:** Admins can view all project communications

### Database Schema and Relationships

**Core Tables:**

**Users Table:**

- id, name, email, password, role_id, access_level_id
- Relationships: belongsTo Role, hasMany Projects, hasMany Tasks

**Projects Table:**

- id, name, job_code, status, client_id, timeline_id, harvest_link, invoice_schedule
- Relationships: belongsTo Client, belongsToMany Users, hasMany Tasks, belongsToMany Phases

**Tasks Table:**

- id, name, description, project_id, status, duration, due_date
- Relationships: belongsTo Project, belongsToMany Users, belongsTo Status

**Roles Table:**

- id, name, description
- Relationships: hasMany Users, belongsToMany Permissions

**Permissions Table:**

- id, name, description
- Relationships: belongsToMany Roles

**Resources Table:**

- id, name, team, pm_hours, designer_hours, developer_hours, cs_hours
- Relationships: belongsTo User

**Holidays Table:**

- id, title, description, start_date, end_date, user_id, is_global, region
- Relationships: belongsTo User

**Project Messages Table:**

- id, project_id, parent_id, subject, message, posted_by
- Relationships: belongsTo Project, belongsTo User, hasMany Files

### API Endpoints and Integration

**Authentication Endpoints:**

- POST /login - User authentication
- POST /logout - User logout
- POST /password/reset - Password reset request

**Projects API:**

- GET /api/projects - List all projects (role-based)
- GET /api/projects/{id} - Get specific project details
- POST /api/projects - Create new project (admin only)
- PUT /api/projects/{id} - Update project (admin only)
- DELETE /api/projects/{id} - Archive project (admin only)

**Tasks API:**

- GET /api/tasks - List tasks (filtered by user permissions)
- GET /api/tasks/{id} - Get specific task details
- POST /api/tasks - Create new task
- PUT /api/tasks/{id} - Update task
- DELETE /api/tasks/{id} - Delete task

**Calendar API:**

- GET /api/calendar/events - Get calendar events
- POST /api/calendar/holidays - Add holiday (admin/user based)
- PUT /api/calendar/holidays/{id} - Update holiday
- DELETE /api/calendar/holidays/{id} - Delete holiday

**Resource Allocation API:**

- GET /api/resources/allocation - Get resource allocation data
- GET /api/resources/utilization - Get team utilization metrics

### Security Implementation

**Authentication System:**

- Laravel's built-in authentication
- Session-based authentication for web interface
- Password hashing using bcrypt
- Remember token functionality

**Authorization Middleware:**

- **SuperAdminPermissionMiddleware:** Restricts access to super admin features
- **TaskPermissionMiddleware:** Controls task-related permissions
- **isBrand:** Brand-specific access control

**Role-Based Access Control:**

- Permission-based system with role assignments
- Middleware validation for route protection
- Dynamic permission checking in views
- Access level overrides for enhanced permissions

**Data Protection:**

- Input validation and sanitization
- CSRF protection on all forms
- SQL injection prevention through Eloquent ORM
- File upload validation and type checking

### Troubleshooting Guide

**Common Issues:**

**Login Problems:**

- Incorrect credentials: Verify email and password
- Account locked: Contact admin for account reset
- Role assignment issues: Check user role and access level
- Session timeout: Re-login required

**Permission Errors:**

- Access denied messages: Check user role and permissions
- Missing features: Verify access level assignment
- Project visibility: Ensure user is assigned to project
- Admin functions unavailable: Confirm admin role or access level

**File Upload Issues:**

- File size exceeded: Maximum 20MB per file
- Unsupported file type: Check allowed file extensions
- Upload timeout: Check server configuration
- Storage space: Verify available disk space

**Calendar Sync Problems:**

- Events not displaying: Check date range and filters
- Holiday conflicts: Verify regional settings
- PTO not showing: Confirm approval status
- Timezone issues: Check user timezone settings

**Performance Issues:**

- Slow loading: Check database queries and indexing
- Memory errors: Optimize Livewire component state
- Timeout errors: Increase server timeout limits
- Cache issues: Clear application and browser cache

**Error Messages Reference:**

**Authentication Errors:**

- "Invalid credentials": Username/password incorrect
- "Account suspended": User account deactivated
- "Access denied": Insufficient permissions
- "Session expired": Re-authentication required

**Database Errors:**

- "Connection failed": Database connectivity issues
- "Query timeout": Long-running database operations
- "Constraint violation": Data integrity issues
- "Table not found": Missing database migrations

**File System Errors:**

- "Upload failed": File upload process interrupted
- "File not found": Requested file doesn't exist
- "Permission denied": File system permission issues
- "Disk full": Insufficient storage space

### FAQ Section

**User Questions:**

Q: How do I request time off?
A: Use the PTO Calendar feature to add personal holidays. Admins can approve requests.

Q: Can I see all projects in the system?
A: Visibility depends on your role. Team members see assigned projects, while admins see all projects.

Q: How do I mention someone in a comment?
A: Type @ followed by the person's name. A dropdown will appear with suggestions.

Q: Why can't I see certain tasks?
A: Task visibility is role-based. You can only see tasks assigned to you unless you have admin permissions.

**Admin Questions:**

Q: How do I add a new user?
A: Go to User Management in the admin dashboard and click "Add New User."

Q: Can I change someone's role after creation?
A: Yes, edit the user profile and update their role and access level.

Q: How do I set up global holidays?
A: Use the Holiday Management feature and check the "Global" option when creating holidays.

Q: How do I track resource utilization?
A: Use the Resource Allocation view to see weekly utilization percentages and hour allocations.

**Technical Questions:**

Q: What PHP version is required?
A: PHP 8.1 or higher is required for the application.

Q: How do I backup the database?
A: Use standard MySQL backup procedures or Laravel's database backup packages.

Q: Can I integrate with external time tracking?
A: Yes, Harvest integration is built-in. Configure the Harvest links in project settings.

Q: How do I update the application?
A: Follow standard Laravel update procedures: composer update, migrate, and clear cache.

### Support and Maintenance

**Internal Support:**

- System administrators for user account issues
- Project managers for workflow questions
- Technical team for system problems
- Training resources for new users

**Technical Support:**

- Database maintenance and optimization
- Server configuration and updates
- Security patches and updates
- Performance monitoring and tuning

**User Training:**

- New user onboarding sessions
- Feature-specific training modules
- Best practices documentation
- Video tutorials and guides

**System Maintenance:**

- Regular database backups
- Security updates and patches
- Performance optimization
- Feature updates and enhancements
