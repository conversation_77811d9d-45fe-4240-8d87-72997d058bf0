# ShieldsGF Portal Documentation Structure

## Main Documentation Sections

### 1. Getting Started

- Overview
- Installation & Setup
- System Requirements
- Configuration

### 2. User Guide

- Authentication & Login
- Dashboard Overview
- User Roles & Permissions
- Navigation

### 3. Core Features

- Project Management
- Task Management
- Status Hub
- Calendar & PTO Management
- Resource Allocation
- Team Management
- File Management
- Messaging System

### 4. Admin Features

- Admin Dashboard
- User Management
- Role & Permission Management
- System Configuration
- Holiday Management

### 5. API Reference

- Authentication
- Projects API
- Tasks API
- Users API
- Calendar API

### 6. Technical Documentation

- Architecture Overview
- Database Schema
- Livewire Components
- Middleware & Security
- File Structure

### 7. Troubleshooting

- Common Issues
- Error Messages
- FAQ

## File Structure for Documentation

```
docs/
├── index.html                 # Main documentation homepage
├── assets/
│   ├── css/
│   │   └── docs.css          # Documentation styling
│   ├── js/
│   │   ├── docs.js           # Main documentation JavaScript
│   │   └── data.js           # All documentation data
│   └── images/               # Screenshots and diagrams
├── getting-started/
│   ├── overview.html
│   ├── installation.html
│   └── configuration.html
├── user-guide/
│   ├── authentication.html
│   ├── dashboard.html
│   ├── roles-permissions.html
│   └── navigation.html
├── features/
│   ├── project-management.html
│   ├── task-management.html
│   ├── status-hub.html
│   ├── calendar-pto.html
│   ├── resource-allocation.html
│   ├── team-management.html
│   ├── file-management.html
│   └── messaging.html
├── admin/
│   ├── admin-dashboard.html
│   ├── user-management.html
│   ├── role-management.html
│   ├── system-config.html
│   └── holiday-management.html
├── api/
│   ├── authentication.html
│   ├── projects.html
│   ├── tasks.html
│   ├── users.html
│   └── calendar.html
├── technical/
│   ├── architecture.html
│   ├── database.html
│   ├── livewire.html
│   ├── security.html
│   └── file-structure.html
└── troubleshooting/
    ├── common-issues.html
    ├── error-messages.html
    └── faq.html
```

## Data Structure for docs.js

The documentation data will be structured as follows:

```javascript
const docsData = {
  overview: { ... },
  gettingStarted: { ... },
  userGuide: { ... },
  features: { ... },
  admin: { ... },
  api: { ... },
  technical: { ... },
  troubleshooting: { ... }
}
```

Each section will contain:

- title
- description
- content (HTML)
- subsections
- code examples
- screenshots
- related links

## project overview

shield sgf portal is the software for managing the projects and clients efficiently if makes a bridge between clients and Developers to make things smooth and work efficiently

## Roles

there are different roles in the system

- SuperAdmin
  -Admin
  -Team Member
  -Client Member
  -Enterprise Owner

Each Role Elaboration

- SuperAdmin: has all the permissions to manage the system and users
- Admin: has permissions to manage projects, tasks, users.
- Team Member: can manage tasks assigned to them and view project details

Important Note-Some team Member can have admin access level which will give them admin level permissions.

Client Member: can view project details and communicate with team members.

EnterPrise Owner: can view project details and can see all the projects of their clients and communicate with team members.

Data Flow
User->login->system validates all credentials and user role->validation failed->back to login page->validation success->redirect to dashboard based on user role

Different Dashboards-

- SuperAdmin Dashboard
- Admin Dashboard
- Team Member Dashboard
- Client Member Dashboard
- Enterprise Owner Dashboard

What is visible in each dashboard

- SuperAdmin Dashboard: User Management, System Settings, All Projects Overview, All Tasks,PTO Calender, resource allocation and Status Hub.
- Admin Dashboard: User Management, All Projects Overview, All Tasks,PTO Calender and resource allocation

- Team Member Dashboard-Tasks Assigned to them, Projects they are working on, PTO Calender.

- Client Member Dashboard-Their Projects and what currently is going on with the project.

- Enterprise Owner Dashboard-Clients Projects and what currently is going on with the project.

How they are connected with each other

Let's say a new project is added by a admin or a team member who has admin access,

First we have to understand how project is created, for creating a project it is mandatory to have a client.
The clents may or may not have a brand (enterprise) associated with them.

To create a project these fields are mandatory

- AI will add this

Now Team members are connected to the project.

Now tasks are created for the project and assigned to the team members.

Tasks can be of different types
-Urgent
-New
-Feedback
-Completed
-In Progress
-Recently Finished

A task can have due date

Comments in Task-

- Rich text editor with formatting options (bold, italic, underline)
- User mentions with @ symbol and dropdown suggestions
- Line breaks supported with Shift+Enter
- Image paste functionality
- Link highlighting and auto-formatting
- Comment threading and replies

## Detailed Feature Documentation

### PTO Calendar System - Complete Workflow

**Admin Capabilities:**

- Add global holidays for all users (company-wide)
- Choose holiday regions (Indian holidays or US holidays)
- Add personal leave for individual team members
- View all team member holidays in calendar view
- Edit and delete holiday events
- Set holiday descriptions and date ranges

**User Capabilities:**

- Add personal holidays/PTO requests
- View team calendar with all holidays
- See project completion dates (displayed in blue)
- View phase completion dates (color-coded by phase index)
- Click on events to see complete details

**Calendar Features:**

- Interactive calendar view with navigation controls
- AJAX-based event addition (no page refresh required)
- Clickable events show detailed information
- Personal holidays display user names (e.g., "John is out")
- Integration with resource allocation calculations
- Holiday events appear at the end of resource allocation display

**Technical Implementation:**

- Holiday model with user relationships
- Global vs personal holiday flags
- Region-based holiday filtering
- Real-time calendar updates via Livewire

### Status Hub - Project Management Center

**Core Functionality:**

- Visual representation of all projects in different phases
- Drag-and-drop project movement between phases
- Real-time status updates and progress tracking
- Project archiving and restoration capabilities

**Project Display Elements:**

- Project boxes with color-coded phases
- Progress indicators showing completion percentage
- Job codes and project names
- Client information display
- Team member flags with individual color codes
- Phase completion status indicators

**Interactive Features:**

- Drag projects between phase columns
- Click project boxes for detailed project view
- Archive/unarchive projects
- Real-time updates without page refresh
- Project filtering and search capabilities

**Phase Management:**

- Projects divided by categories within phases
- Category names displayed instead of phase names
- Phase completion dates tracked
- Project completion determined by highest phase ID target date

### Client Dashboard - Client Portal Features

**Active Projects Section:**

- Current projects in progress with phase indicators
- Progress visualization (e.g., "PHASE 2/5")
- Direct links to project details
- Team member information
- Timeline and milestone tracking

**Voyager Projects Section:**

- Completed projects in maintenance phase
- Access to site analytics
- Billing history and invoicing
- Ongoing support status
- Performance metrics

**Completed Projects Archive:**

- Historical project information
- Final deliverables access
- Project summaries and outcomes
- Client feedback and testimonials

**Communication Tools:**

- Direct messaging with project teams
- File sharing and document access
- Project update notifications
- Meeting scheduling and notes

**Quick Access Features:**

- Project-specific resource links
- Important document downloads
- Contact information for team members
- Support ticket creation

### Resource Allocation System - Comprehensive Guide

**Calculation Methodology:**

- **Standard Work Week:** 32 hours (6+ hours per day)
- **Percentage Calculations:** Based on 40-hour work week for standardization
- **Leave Integration:** Automatically accounts for holidays and PTO
- **Multi-role Support:** Users can have different hour allocations for different roles

**Role-Based Hour Allocation:**

- **Developer Hours:** Pulled from resources table per user
- **Designer Hours:** Pulled from resources table per user
- **Project Manager Hours:** 20-30% allocation during design, code, deploy, and manage phases
- **Customer Success Hours:** Tracked separately in resources table

**Display Format and Features:**

- **Hour Display:** Shows both percentage and actual hours "(allocated/available) hours"
- **Calendar View:** Uses same layout as calendar view (not tabular format)
- **Weekly Tracking:** Displays weekly utilization for each team member
- **Extended Timeline:** Shows weeks beyond current month until all project phases complete
- **Project Transitions:** Tracks how resources move between teams when phases complete

**Advanced Calculations:**

- **Remaining Hours:** Calculates remaining project hours by tracking used hours in each phase
- **Phase Tracking:** Monitors weekly transitions between project phases
- **Utilization Rates:** Can exceed 100% allocation when workload is high
- **Team Transitions:** Tracks resource movement between projects and phases

**Visual Elements:**

- **Reduced Event Height:** Allows more events to display simultaneously
- **Holiday Placement:** Holiday events appear at end of resource allocation percentages
- **No Week Numbers:** Week numbers removed from calendar events for cleaner display
- **Project Names:** Fully visible (not truncated) with clickable details
- **Team Details:** Shows developer, designer, and PM team information

### Task Management System - Complete Workflow

**Task Categories and Display:**

- **Urgent Tasks:** Displayed with star icons for immediate attention
- **New Tasks:** Special indicators for recently created tasks
- **Regular Tasks:** Simple display without special icons
- **Feedback Tasks:** Tasks requiring client or team feedback
- **In Progress:** Currently active tasks
- **Completed:** Finished tasks
- **Recently Finished:** Recently completed tasks (visible only with specific filter)

**Access Control by Role:**

- **Admin/SuperAdmin:** Can view all tasks across all projects
- **Regular Users:** Can only see tasks assigned to them
- **Project-Specific Views:** Tasks filtered by specific project context
- **Client Members:** Can view project-related tasks (limited visibility)

**Task Creation and Management:**

- **Mandatory Fields:** Task name, description, project assignment
- **Optional Fields:** Due date, priority level, estimated duration
- **Assignment:** Can assign to multiple team members
- **Status Workflow:** Defined progression through task states
- **Attachments:** Support for file uploads and attachments

**Rich Text Features:**

- **Formatting Options:** Bold (Ctrl+B), Italic (Ctrl+I), Underline (Ctrl+U)
- **User Mentions:** @ symbol triggers dropdown with user suggestions
- **Line Breaks:** Shift+Enter for line breaks within comments
- **Image Support:** Paste images directly into comments
- **Link Handling:** Automatic link detection and formatting
- **Link Behavior:** Links open in new windows (target='\_blank')

**Comment System:**

- **Threading:** Nested comment replies
- **Mention Replacement:** Selected mentions replace partial typed names
- **Visual Feedback:** Selected text highlighted when formatting applied
- **Toggle Formatting:** Buttons can toggle formatting on/off
- **Timezone Handling:** Timestamps display correctly for user's timezone

### Project Management - Complete Lifecycle

**Project Creation Requirements:**

- **Client Assignment:** Must have existing client (mandatory)
- **Brand Association:** Clients may have associated brands/enterprises
- **Job Code:** Unique identifier for project tracking
- **Project Title:** Descriptive name for the project
- **Timeline:** Project duration and milestones
- **Team Assignment:** Assign team members with specific roles
- **Social Details:** Social media links and information
- **Invoice Schedule:** Billing frequency and terms
- **Harvest Integration:** Link to time tracking system

**Project Phases:**

- **Design Phase:** Initial design and planning
- **Development Phase:** Code implementation and development
- **Deploy Phase:** Testing, deployment, and launch
- **Manage Phase:** Ongoing maintenance and support
- **Voyager Phase:** Long-term maintenance for completed projects

**Phase Management:**

- **Phase Categories:** Projects divided by categories within phases
- **Target Dates:** Each phase has specific target completion dates
- **Progress Tracking:** Visual progress indicators and percentage completion
- **Phase Transitions:** Automatic progression through phases
- **Completion Calculation:** Project completion based on highest phase ID target date

**Project Information Display:**

- **Job Codes:** Unique identifiers (e.g., [SP-003-25])
- **Phase Indicators:** Current phase and total phases (e.g., "PHASE 2/5")
- **Team Flags:** Color-coded team member indicators
- **Client Information:** Associated client and brand details
- **Status Updates:** Real-time project status changes

### Team Management and User Roles

**Role Hierarchy and Permissions:**

**SuperAdmin:**

- Complete system access and control
- User creation, modification, and deletion
- Role and permission management
- System configuration and settings
- Access to all projects and data
- Database management capabilities

**Admin:**

- Project creation and management
- Team member assignment and oversight
- Resource allocation and planning
- Task creation and assignment
- Access to admin dashboard features
- User management (limited)

**Team Members:**

- Task management for assigned tasks
- Project collaboration and communication
- Time tracking and reporting
- File uploads and sharing
- Calendar access for PTO requests
- Limited project visibility

**Team Members with Admin Access Level:**

- Regular team member base permissions
- Additional admin-level permissions
- Project management capabilities
- Enhanced system access
- Resource allocation visibility

**Client Members:**

- Project visibility for their projects only
- Communication with assigned team members
- File access and document downloads
- Project progress tracking
- Limited system interaction

**Enterprise Owners:**

- Access to all client projects under their enterprise
- Multi-client project oversight
- Communication with project teams
- Reporting and analytics access
- Brand-level project management

### File Management System

**Upload Capabilities:**

- **File Size Limit:** 20MB maximum per file
- **Multiple Uploads:** Support for multiple file selection
- **File Types:** Support for documents, images, videos, and archives
- **Project Organization:** Files organized by project structure
- **Version Control:** Track file versions and changes

**Storage Structure:**

- **Project-Based:** Files stored in project-specific directories
- **Message Attachments:** Files linked to project messages
- **Task Attachments:** Files associated with specific tasks
- **User Uploads:** Personal file storage areas

**Access Control:**

- **Role-Based Access:** File visibility based on user roles
- **Project Team Access:** Team members can access project files
- **Client Visibility:** Clients can access relevant project files
- **Admin Override:** Admins have access to all files

**File Operations:**

- **Upload:** Drag-and-drop or browse file selection
- **Download:** Direct file download with access logging
- **Preview:** In-browser preview for supported file types
- **Sharing:** Share files with specific team members or clients
- **Organization:** Folder structure and file categorization
