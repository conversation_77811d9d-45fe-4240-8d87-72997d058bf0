// Node.js script to generate markdown-content.js from structure.md
// Run with: node generate-content.cjs

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

try {
    // Read the structure.md file
    const markdownPath = path.join(__dirname, 'structure.md');
    const markdownContent = fs.readFileSync(markdownPath, 'utf8');
    
    // Escape backticks and template literals in the content
    const escapedContent = markdownContent
        .replace(/\\/g, '\\\\')  // Escape backslashes
        .replace(/`/g, '\\`')    // Escape backticks
        .replace(/\${/g, '\\${'); // Escape template literals
    
    // Create the JavaScript file content
    const jsContent = `// ShieldsGF Portal Documentation Content
// This file contains the markdown content embedded as a JavaScript string
// to avoid CORS issues when opening directly in browser
// Auto-generated from structure.md - do not edit manually

const markdownContent = \`${escapedContent}\`;

// Export the content
window.markdownContent = markdownContent;`;

    // Write the JavaScript file
    const outputPath = path.join(__dirname, 'assets', 'js', 'markdown-content.js');
    fs.writeFileSync(outputPath, jsContent, 'utf8');
    
    console.log('✅ Successfully generated markdown-content.js');
    console.log(`📄 Source: ${markdownPath}`);
    console.log(`📄 Output: ${outputPath}`);
    console.log(`📊 Content length: ${markdownContent.length} characters`);
    
} catch (error) {
    console.error('❌ Error generating content:', error.message);
    process.exit(1);
}
