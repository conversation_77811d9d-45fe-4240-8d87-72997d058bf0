// ShieldsGF Portal Documentation Data
const docsData = {
  overview: {
    id: 'overview',
    title: 'ShieldsGF Portal - Complete Documentation',
    description: 'Comprehensive project management and team collaboration platform',
    icon: 'fas fa-home',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-shield-alt me-3"></i>ShieldsGF Portal Overview</h1>

        <div class="info-card">
          <h4>What is ShieldsGF Portal?</h4>
          <p>ShieldsGF Portal is a comprehensive project management and team collaboration platform designed to bridge the gap between clients and development teams. It provides a centralized hub for managing projects, tracking progress, allocating resources, and facilitating seamless communication throughout the project lifecycle.</p>
        </div>

        <h2>Core Purpose</h2>
        <div class="row">
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-rocket me-2"></i>Streamline Workflows</h4>
              <p>Complete project workflows from inception to completion with automated processes and clear milestone tracking.</p>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-eye me-2"></i>Transparent Visibility</h4>
              <p>Provide transparent project visibility for all stakeholders with real-time updates and comprehensive reporting.</p>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-users me-2"></i>Optimize Resources</h4>
              <p>Advanced resource allocation and team utilization tracking for maximum efficiency and productivity.</p>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-comments me-2"></i>Facilitate Communication</h4>
              <p>Seamless communication between teams and clients with integrated messaging and notification systems.</p>
            </div>
          </div>
        </div>

        <h2>Key Features & Benefits</h2>

        <div class="info-card">
          <h4><i class="fas fa-project-diagram me-2"></i>Project Management Excellence</h4>
          <ul>
            <li><strong>Complete Lifecycle Management:</strong> Track projects from design through deployment and maintenance</li>
            <li><strong>Visual Status Hub:</strong> Drag-and-drop project phase transitions with real-time updates</li>
            <li><strong>Phase Tracking:</strong> Detailed phase management with progress indicators and milestone tracking</li>
            <li><strong>Client Portals:</strong> Dedicated client interfaces for project transparency and communication</li>
            <li><strong>Progress Monitoring:</strong> Real-time progress tracking with comprehensive reporting</li>
          </ul>
        </div>
      </div>
    `,
    subsections: [
      { id: 'core-purpose', title: 'Core Purpose' },
      { id: 'key-features', title: 'Key Features & Benefits' },
      { id: 'technology-stack', title: 'Technology Stack' },
      { id: 'architecture-principles', title: 'Architecture Principles' }
    ]
  },

  gettingStarted: {
    id: 'getting-started',
    title: 'Getting Started Guide',
    description: 'Complete setup and installation guide',
    icon: 'fas fa-rocket',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-rocket me-3"></i>Getting Started</h1>

        <div class="warning-card">
          <h4><i class="fas fa-exclamation-triangle me-2"></i>Prerequisites</h4>
          <p>Before installing ShieldsGF Portal, ensure you have the required system components and development environment set up properly.</p>
        </div>

        <h2>System Requirements</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-server me-2"></i>Server Requirements</h4>
              <ul>
                <li><strong>PHP:</strong> 8.2 or higher with required extensions</li>
                <li><strong>Web Server:</strong> Apache 2.4+ or Nginx 1.18+</li>
                <li><strong>Database:</strong> MySQL 8.0+ or MariaDB 10.4+</li>
                <li><strong>Memory:</strong> Minimum 512MB RAM (2GB+ recommended)</li>
                <li><strong>Storage:</strong> 1GB+ available disk space</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-code me-2"></i>Development Environment</h4>
              <ul>
                <li><strong>Node.js:</strong> 16.x or higher for asset compilation</li>
                <li><strong>Composer:</strong> Latest version for PHP dependencies</li>
                <li><strong>Git:</strong> For version control and deployment</li>
                <li><strong>NPM/Yarn:</strong> Package manager for frontend dependencies</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Installation Guide</h2>

        <div class="info-card">
          <h4>Step 1: Clone Repository</h4>
          <pre><code class="language-bash">git clone [repository-url] sgf-portal
cd sgf-portal</code></pre>
        </div>

        <div class="info-card">
          <h4>Step 2: Install Dependencies</h4>
          <pre><code class="language-bash"># Install PHP dependencies
composer install

# Install Node.js dependencies
npm install</code></pre>
        </div>

        <div class="success-card">
          <h4><i class="fas fa-user-shield me-2"></i>Default Admin Account</h4>
          <p>After running the database seeder, a default SuperAdmin account is created:</p>
          <ul>
            <li><strong>Email:</strong> <EMAIL></li>
            <li><strong>Password:</strong> admin123 <em>(change immediately after first login)</em></li>
          </ul>
        </div>
      </div>
    `,
    subsections: [
      { id: 'system-requirements', title: 'System Requirements' },
      { id: 'installation-guide', title: 'Installation Guide' },
      { id: 'initial-configuration', title: 'Initial Configuration' },
      { id: 'first-login-setup', title: 'First Login & Setup' }
    ]
  },

  userGuide: {
    title: "User Guide",
    description: "Complete guide for end users",
    sections: {
      authentication: {
        title: "Authentication & Login",
        content: `
          <h2>Logging In</h2>
          <p>Access the portal through your organization's URL and use your assigned credentials.</p>
          
          <h3>Login Process</h3>
          <ol>
            <li>Navigate to the login page</li>
            <li>Enter your email address</li>
            <li>Enter your password</li>
            <li>Click "Sign In"</li>
          </ol>

          <h3>Dashboard Redirection</h3>
          <p>After login, you'll be redirected based on your role:</p>
          <ul>
            <li><strong>SuperAdmin/Admin:</strong> Admin Dashboard</li>
            <li><strong>Client Members:</strong> Client Dashboard</li>
            <li><strong>Brand Members:</strong> Brand Dashboard</li>
            <li><strong>Team Members:</strong> Standard Dashboard</li>
          </ul>

          <h3>Password Reset</h3>
          <p>If you forget your password, use the "Forgot Password" link on the login page.</p>
        `
      },
      dashboard: {
        title: "Dashboard Overview",
        content: `
          <h2>Dashboard Components</h2>
          
          <h3>Quick Stats</h3>
          <p>View key metrics at a glance:</p>
          <ul>
            <li>Active projects count</li>
            <li>Pending tasks</li>
            <li>Team utilization</li>
            <li>Upcoming deadlines</li>
          </ul>

          <h3>Recent Activity</h3>
          <p>Stay updated with:</p>
          <ul>
            <li>Recent task updates</li>
            <li>Project status changes</li>
            <li>New messages</li>
            <li>File uploads</li>
          </ul>

          <h3>Quick Actions</h3>
          <p>Access frequently used features:</p>
          <ul>
            <li>Create new task</li>
            <li>View calendar</li>
            <li>Access status hub</li>
            <li>Open Harvest (time tracking)</li>
          </ul>
        `
      }
    }
  }
};

// Navigation structure for the documentation
const navigationStructure = [
  {
    title: 'Introduction & Overview',
    icon: 'fas fa-home',
    id: 'introduction',
    items: [
      { title: 'Overview', id: 'overview', icon: 'fas fa-info-circle' },
      { title: 'Key Features', id: 'key-features', icon: 'fas fa-star' },
      { title: 'Technology Stack', id: 'technology-stack', icon: 'fas fa-layer-group' },
      { title: 'Architecture', id: 'architecture', icon: 'fas fa-sitemap' }
    ]
  },
  {
    title: 'Getting Started',
    icon: 'fas fa-rocket',
    id: 'getting-started-section',
    items: [
      { title: 'System Requirements', id: 'getting-started', icon: 'fas fa-server' },
      { title: 'Installation Guide', id: 'installation', icon: 'fas fa-download' },
      { title: 'Configuration', id: 'configuration', icon: 'fas fa-cog' },
      { title: 'First Login', id: 'first-login', icon: 'fas fa-sign-in-alt' }
    ]
  },
  {
    title: 'User Roles & Access',
    icon: 'fas fa-users',
    id: 'user-roles',
    items: [
      { title: 'Role Hierarchy', id: 'role-hierarchy', icon: 'fas fa-sitemap' },
      { title: 'Permission Matrix', id: 'permission-matrix', icon: 'fas fa-table' },
      { title: 'Dashboard Variations', id: 'dashboard-variations', icon: 'fas fa-tachometer-alt' },
      { title: 'Access Levels', id: 'access-levels', icon: 'fas fa-key' }
    ]
  },
  {
    title: 'Core Features',
    icon: 'fas fa-cogs',
    id: 'core-features',
    items: [
      { title: 'Project Management', id: 'project-management', icon: 'fas fa-project-diagram' },
      { title: 'Task Management', id: 'task-management', icon: 'fas fa-tasks' },
      { title: 'Status Hub', id: 'status-hub', icon: 'fas fa-chart-bar' },
      { title: 'PTO Calendar', id: 'pto-calendar', icon: 'fas fa-calendar-alt' },
      { title: 'Resource Allocation', id: 'resource-allocation', icon: 'fas fa-chart-pie' },
      { title: 'Team Collaboration', id: 'team-collaboration', icon: 'fas fa-comments' },
      { title: 'Real-time Features', id: 'real-time-features', icon: 'fas fa-bolt' },
      { title: 'File Management', id: 'file-management', icon: 'fas fa-folder' }
    ]
  },
  {
    title: 'Admin Features',
    icon: 'fas fa-user-shield',
    id: 'admin-features',
    items: [
      { title: 'Admin Dashboard', id: 'admin-dashboard', icon: 'fas fa-tachometer-alt' },
      { title: 'User Management', id: 'user-management', icon: 'fas fa-users-cog' },
      { title: 'System Configuration', id: 'system-config', icon: 'fas fa-cog' },
      { title: 'Permissions', id: 'permissions', icon: 'fas fa-shield-alt' }
    ]
  },
  {
    title: 'Technical Documentation',
    icon: 'fas fa-code',
    id: 'technical',
    items: [
      { title: 'Architecture', id: 'architecture', icon: 'fas fa-sitemap' },
      { title: 'Database Schema', id: 'database', icon: 'fas fa-database' },
      { title: 'API Reference', id: 'api', icon: 'fas fa-plug' },
      { title: 'Security', id: 'security', icon: 'fas fa-lock' }
    ]
  }
];

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { docsData, navigationStructure };
}
