// ShieldsGF Portal Documentation Data
const docsData = {
  overview: {
    title: "ShieldsGF Portal Overview",
    description: "Comprehensive project management and team collaboration platform",
    content: `
      <h2>What is ShieldsGF Portal?</h2>
      <p>ShieldsGF Portal is a comprehensive project management and team collaboration platform designed to streamline workflows, manage resources, and track project progress efficiently.</p>
      
      <h3>Key Features</h3>
      <ul>
        <li><strong>Project Management:</strong> Complete project lifecycle management with phases, timelines, and status tracking</li>
        <li><strong>Task Management:</strong> Detailed task assignment, tracking, and collaboration</li>
        <li><strong>Resource Allocation:</strong> Weekly resource planning and utilization tracking</li>
        <li><strong>Calendar Integration:</strong> PTO management, holidays, and project milestones</li>
        <li><strong>Team Collaboration:</strong> Messaging, file sharing, and real-time updates</li>
        <li><strong>Role-Based Access:</strong> Granular permissions for different user types</li>
      </ul>

      <h3>User Types</h3>
      <div class="user-types">
        <div class="user-type">
          <h4>SuperAdmin</h4>
          <p>Full system access, user management, system configuration</p>
        </div>
        <div class="user-type">
          <h4>Admin</h4>
          <p>Project management, team oversight, resource allocation</p>
        </div>
        <div class="user-type">
          <h4>Team Members</h4>
          <p>Task management, project collaboration, time tracking</p>
        </div>
        <div class="user-type">
          <h4>Client Members</h4>
          <p>Project visibility, communication, file access</p>
        </div>
        <div class="user-type">
          <h4>Brand Members</h4>
          <p>Brand-specific project access and collaboration</p>
        </div>
      </div>
    `,
    subsections: [
      { id: "features", title: "Key Features" },
      { id: "user-types", title: "User Types" },
      { id: "architecture", title: "System Architecture" }
    ]
  },

  gettingStarted: {
    title: "Getting Started",
    description: "Installation, setup, and initial configuration",
    sections: {
      installation: {
        title: "Installation & Setup",
        content: `
          <h2>System Requirements</h2>
          <ul>
            <li>PHP 8.1 or higher</li>
            <li>Laravel 11.x</li>
            <li>MySQL 8.0 or higher</li>
            <li>Node.js 16+ (for asset compilation)</li>
            <li>Composer</li>
          </ul>

          <h2>Installation Steps</h2>
          <ol>
            <li>Clone the repository</li>
            <li>Install PHP dependencies: <code>composer install</code></li>
            <li>Install Node dependencies: <code>npm install</code></li>
            <li>Copy environment file: <code>cp .env.example .env</code></li>
            <li>Generate application key: <code>php artisan key:generate</code></li>
            <li>Configure database settings in .env</li>
            <li>Run migrations: <code>php artisan migrate</code></li>
            <li>Seed database: <code>php artisan db:seed</code></li>
            <li>Compile assets: <code>npm run build</code></li>
          </ol>
        `
      },
      configuration: {
        title: "Configuration",
        content: `
          <h2>Environment Configuration</h2>
          <p>Key configuration settings in your .env file:</p>
          
          <h3>Database</h3>
          <pre><code>DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sgf_portal
DB_USERNAME=your_username
DB_PASSWORD=your_password</code></pre>

          <h3>Mail Configuration</h3>
          <pre><code>MAIL_MAILER=smtp
MAIL_HOST=your_smtp_host
MAIL_PORT=587
MAIL_USERNAME=your_email
MAIL_PASSWORD=your_password</code></pre>

          <h3>File Storage</h3>
          <pre><code>FILESYSTEM_DISK=local
# For production, consider using S3 or similar</code></pre>
        `
      }
    }
  },

  userGuide: {
    title: "User Guide",
    description: "Complete guide for end users",
    sections: {
      authentication: {
        title: "Authentication & Login",
        content: `
          <h2>Logging In</h2>
          <p>Access the portal through your organization's URL and use your assigned credentials.</p>
          
          <h3>Login Process</h3>
          <ol>
            <li>Navigate to the login page</li>
            <li>Enter your email address</li>
            <li>Enter your password</li>
            <li>Click "Sign In"</li>
          </ol>

          <h3>Dashboard Redirection</h3>
          <p>After login, you'll be redirected based on your role:</p>
          <ul>
            <li><strong>SuperAdmin/Admin:</strong> Admin Dashboard</li>
            <li><strong>Client Members:</strong> Client Dashboard</li>
            <li><strong>Brand Members:</strong> Brand Dashboard</li>
            <li><strong>Team Members:</strong> Standard Dashboard</li>
          </ul>

          <h3>Password Reset</h3>
          <p>If you forget your password, use the "Forgot Password" link on the login page.</p>
        `
      },
      dashboard: {
        title: "Dashboard Overview",
        content: `
          <h2>Dashboard Components</h2>
          
          <h3>Quick Stats</h3>
          <p>View key metrics at a glance:</p>
          <ul>
            <li>Active projects count</li>
            <li>Pending tasks</li>
            <li>Team utilization</li>
            <li>Upcoming deadlines</li>
          </ul>

          <h3>Recent Activity</h3>
          <p>Stay updated with:</p>
          <ul>
            <li>Recent task updates</li>
            <li>Project status changes</li>
            <li>New messages</li>
            <li>File uploads</li>
          </ul>

          <h3>Quick Actions</h3>
          <p>Access frequently used features:</p>
          <ul>
            <li>Create new task</li>
            <li>View calendar</li>
            <li>Access status hub</li>
            <li>Open Harvest (time tracking)</li>
          </ul>
        `
      }
    }
  }
};

// Navigation structure
const navigationStructure = [
  {
    title: "Getting Started",
    items: [
      { title: "Overview", id: "overview" },
      { title: "Installation", id: "installation" },
      { title: "Configuration", id: "configuration" }
    ]
  },
  {
    title: "User Guide",
    items: [
      { title: "Authentication", id: "authentication" },
      { title: "Dashboard", id: "dashboard" },
      { title: "Navigation", id: "navigation" }
    ]
  },
  {
    title: "Features",
    items: [
      { title: "Project Management", id: "projects" },
      { title: "Task Management", id: "tasks" },
      { title: "Status Hub", id: "status-hub" },
      { title: "Calendar & PTO", id: "calendar" },
      { title: "Resource Allocation", id: "resources" },
      { title: "Team Management", id: "teams" },
      { title: "File Management", id: "files" },
      { title: "Messaging", id: "messaging" }
    ]
  },
  {
    title: "Admin Features",
    items: [
      { title: "Admin Dashboard", id: "admin-dashboard" },
      { title: "User Management", id: "user-management" },
      { title: "Role Management", id: "role-management" },
      { title: "System Config", id: "system-config" }
    ]
  },
  {
    title: "Technical",
    items: [
      { title: "Architecture", id: "architecture" },
      { title: "Database Schema", id: "database" },
      { title: "API Reference", id: "api" },
      { title: "Security", id: "security" }
    ]
  }
];

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { docsData, navigationStructure };
}
