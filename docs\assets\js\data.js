// ShieldsGF Portal Documentation Data
const docsData = {
  overview: {
    id: 'overview',
    title: 'ShieldsGF Portal - Complete Documentation',
    description: 'Comprehensive project management and team collaboration platform',
    icon: 'fas fa-home',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-shield-alt me-3"></i>ShieldsGF Portal Overview</h1>

        <div class="info-card">
          <h4>What is ShieldsGF Portal?</h4>
          <p>ShieldsGF Portal is a comprehensive project management and team collaboration platform designed to bridge the gap between clients and development teams. It provides a centralized hub for managing projects, tracking progress, allocating resources, and facilitating seamless communication throughout the project lifecycle.</p>
        </div>

        <h2>Core Purpose</h2>
        <div class="row">
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-rocket me-2"></i>Streamline Workflows</h4>
              <p>Complete project workflows from inception to completion with automated processes and clear milestone tracking.</p>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-eye me-2"></i>Transparent Visibility</h4>
              <p>Provide transparent project visibility for all stakeholders with real-time updates and comprehensive reporting.</p>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-users me-2"></i>Optimize Resources</h4>
              <p>Advanced resource allocation and team utilization tracking for maximum efficiency and productivity.</p>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-comments me-2"></i>Facilitate Communication</h4>
              <p>Seamless communication between teams and clients with integrated messaging and notification systems.</p>
            </div>
          </div>
        </div>

        <h2>Key Features & Benefits</h2>

        <div class="info-card">
          <h4><i class="fas fa-project-diagram me-2"></i>Project Management Excellence</h4>
          <ul>
            <li><strong>Complete Lifecycle Management:</strong> Track projects from design through deployment and maintenance</li>
            <li><strong>Visual Status Hub:</strong> Drag-and-drop project phase transitions with real-time updates</li>
            <li><strong>Phase Tracking:</strong> Detailed phase management with progress indicators and milestone tracking</li>
            <li><strong>Client Portals:</strong> Dedicated client interfaces for project transparency and communication</li>
            <li><strong>Progress Monitoring:</strong> Real-time progress tracking with comprehensive reporting</li>
          </ul>
        </div>
      </div>
    `,
    subsections: [
      { id: 'core-purpose', title: 'Core Purpose' },
      { id: 'key-features', title: 'Key Features & Benefits' },
      { id: 'technology-stack', title: 'Technology Stack' },
      { id: 'architecture-principles', title: 'Architecture Principles' }
    ]
  },

  gettingStarted: {
    id: 'getting-started',
    title: 'Getting Started Guide',
    description: 'Complete setup and installation guide',
    icon: 'fas fa-rocket',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-rocket me-3"></i>Getting Started</h1>

        <div class="warning-card">
          <h4><i class="fas fa-exclamation-triangle me-2"></i>Prerequisites</h4>
          <p>Before installing ShieldsGF Portal, ensure you have the required system components and development environment set up properly.</p>
        </div>

        <h2>System Requirements</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-server me-2"></i>Server Requirements</h4>
              <ul>
                <li><strong>PHP:</strong> 8.2 or higher with required extensions</li>
                <li><strong>Web Server:</strong> Apache 2.4+ or Nginx 1.18+</li>
                <li><strong>Database:</strong> MySQL 8.0+ or MariaDB 10.4+</li>
                <li><strong>Memory:</strong> Minimum 512MB RAM (2GB+ recommended)</li>
                <li><strong>Storage:</strong> 1GB+ available disk space</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-code me-2"></i>Development Environment</h4>
              <ul>
                <li><strong>Node.js:</strong> 16.x or higher for asset compilation</li>
                <li><strong>Composer:</strong> Latest version for PHP dependencies</li>
                <li><strong>Git:</strong> For version control and deployment</li>
                <li><strong>NPM/Yarn:</strong> Package manager for frontend dependencies</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Installation Guide</h2>

        <div class="info-card">
          <h4>Step 1: Clone Repository</h4>
          <pre><code class="language-bash">git clone [repository-url] sgf-portal
cd sgf-portal</code></pre>
        </div>

        <div class="info-card">
          <h4>Step 2: Install Dependencies</h4>
          <pre><code class="language-bash"># Install PHP dependencies
composer install

# Install Node.js dependencies
npm install</code></pre>
        </div>

        <div class="success-card">
          <h4><i class="fas fa-user-shield me-2"></i>Default Admin Account</h4>
          <p>After running the database seeder, a default SuperAdmin account is created:</p>
          <ul>
            <li><strong>Email:</strong> <EMAIL></li>
            <li><strong>Password:</strong> admin123 <em>(change immediately after first login)</em></li>
          </ul>
        </div>
      </div>
    `,
    subsections: [
      { id: 'system-requirements', title: 'System Requirements' },
      { id: 'installation-guide', title: 'Installation Guide' },
      { id: 'initial-configuration', title: 'Initial Configuration' },
      { id: 'first-login-setup', title: 'First Login & Setup' }
    ]
  },

  userRoles: {
    id: 'user-roles',
    title: 'User Roles & Access Control',
    description: 'Comprehensive role-based access control system',
    icon: 'fas fa-users',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-users me-3"></i>User Roles & Access Control</h1>

        <div class="info-card">
          <p>The ShieldsGF Portal implements a sophisticated role-based access control system that ensures appropriate access levels for different user types while maintaining security and operational efficiency.</p>
        </div>

        <h2>Role Hierarchy Explained</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card danger-card">
              <h4><i class="fas fa-crown me-2"></i>SuperAdmin - System Owner</h4>
              <p><strong>Purpose:</strong> Complete system control and oversight</p>
              <p><strong>Scope:</strong> Entire platform and all data</p>
              <ul>
                <li>System configuration and maintenance</li>
                <li>User account creation and management</li>
                <li>Role and permission administration</li>
                <li>Database and security oversight</li>
                <li>Integration management</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card warning-card">
              <h4><i class="fas fa-user-tie me-2"></i>Admin - Project Manager</h4>
              <p><strong>Purpose:</strong> Project and team management</p>
              <p><strong>Scope:</strong> All projects and team members</p>
              <ul>
                <li>Project creation and lifecycle management</li>
                <li>Team member assignment and oversight</li>
                <li>Resource allocation and planning</li>
                <li>Client communication coordination</li>
                <li>Performance monitoring and reporting</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-user me-2"></i>Team Member - Task Executor</h4>
              <p><strong>Purpose:</strong> Task completion and project contribution</p>
              <p><strong>Scope:</strong> Assigned projects and tasks</p>
              <ul>
                <li>Task execution and status updates</li>
                <li>Time tracking and reporting</li>
                <li>Team collaboration and communication</li>
                <li>File uploads and documentation</li>
                <li>Personal PTO management</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-user-plus me-2"></i>Team Member (Admin Access)</h4>
              <p><strong>Purpose:</strong> Enhanced contributor with elevated permissions</p>
              <p><strong>Scope:</strong> Extended project and team visibility</p>
              <ul>
                <li>All team member responsibilities</li>
                <li>Project creation and management</li>
                <li>Limited user management</li>
                <li>Resource allocation oversight</li>
                <li>Advanced reporting access</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-user-friends me-2"></i>Client Member - Project Stakeholder</h4>
              <p><strong>Purpose:</strong> Project visibility and communication</p>
              <p><strong>Scope:</strong> Own projects and related communications</p>
              <ul>
                <li>Project progress monitoring</li>
                <li>Communication with project teams</li>
                <li>File access and review</li>
                <li>Feedback provision</li>
                <li>Invoice and billing review</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-building me-2"></i>Enterprise Owner - Multi-Project Oversight</h4>
              <p><strong>Purpose:</strong> Brand-level project management</p>
              <p><strong>Scope:</strong> All projects under their enterprise/brand</p>
              <ul>
                <li>Multi-project oversight</li>
                <li>Brand-level reporting</li>
                <li>Client relationship management</li>
                <li>Strategic project planning</li>
                <li>Resource allocation review</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Dashboard Variations by Role</h2>

        <div class="info-card">
          <h4><i class="fas fa-tachometer-alt me-2"></i>SuperAdmin/Admin Dashboard</h4>
          <div class="dashboard-mockup">
            <pre>┌─────────────────────────────────────────────────────────────┐
│ 🏠 Admin Dashboard - Happy [Day], [User Name]              │
├─────────────────────────────────────────────────────────────┤
│ 📋 QUICK LINKS                                              │
│ • Clients List        • Project List       • New Project   │
│ • Enterprises List    • New Client         • New Enterprise│
│ ─────────────────────────────────────────────────────────── │
│ • Team Members        • Status Hub         • Harvest       │
│ • PTO Calendar        • Hub Settings                       │
├─────────────────────────────────────────────────────────────┤
│ 📊 TASKS OVERVIEW                          [See All Tasks] │
│ ⭐ Urgent Tasks (with star icons)                          │
│ • [SP-001-25] Project Name - Task Details                  │
│ • [SP-002-25] Project Name - Task Details                  │
│ 🆕 New Tasks (with alert icons)                           │
│ • [SP-003-25] Project Name - Task Details                  │
│ • [SP-004-25] Project Name - Task Details                  │
│                                        [X MORE +]          │
├─────────────────────────────────────────────────────────────┤
│ 📁 YOUR PROJECTS                        [See All Projects] │
│ • [SP-001] Project Name (40% complete)                     │
│ • [SP-002] Project Name (60% complete)                     │
│ • [SP-003] Project Name (25% complete)                     │
│ • [SP-004] Project Name (80% complete)                     │
│ • [SP-005] Project Name (15% complete)                     │
└─────────────────────────────────────────────────────────────┘</pre>
          </div>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-user me-2"></i>Team Member Dashboard</h4>
          <div class="dashboard-mockup">
            <pre>┌─────────────────────────────────────────────────────────────┐
│ 🏠 Team Dashboard - Happy [Day], [User Name]               │
├─────────────────────────────────────────────────────────────┤
│ 📋 QUICK LINKS                                              │
│ • Project List        • Tasks List                         │
│ • Status Hub          • PTO Calendar                       │
├─────────────────────────────────────────────────────────────┤
│ 📊 TASKS OVERVIEW                          [See All Tasks] │
│ ⭐ Urgent Tasks (with star icons)                          │
│ • [SP-001-25] Project Name - Task Details                  │
│ • [SP-002-25] Project Name - Task Details                  │
│ 🆕 New Tasks (with alert icons)                           │
│ • [SP-003-25] Project Name - Task Details                  │
│ • [SP-004-25] Project Name - Task Details                  │
│                                        [X MORE +]          │
├─────────────────────────────────────────────────────────────┤
│ 📁 YOUR PROJECTS                        [See All Projects] │
│ • [SP-001] Project Name (40% complete)                     │
│ • [SP-002] Project Name (60% complete)                     │
│ • [SP-003] Project Name (25% complete)                     │
└─────────────────────────────────────────────────────────────┘</pre>
          </div>
        </div>

        <h2>Access Control Implementation</h2>

        <div class="info-card">
          <h4><i class="fas fa-code me-2"></i>Dashboard Redirection Logic</h4>
          <pre><code class="language-php">public function index(Request $request)
{
    $admin_access_level = AccessLevel::where('name', '=', 'Admin')->first();

    if (Auth::user()->role->name == 'SuperAdmin') {
        return redirect()->route('admin-dashboard');
    } elseif (Auth::user()->role->name == 'Admin') {
        return redirect()->route('admin-dashboard');
    } elseif ($admin_access_level && Auth::user()->access_level_id === $admin_access_level->id) {
        return redirect()->route('admin-dashboard');
    } elseif(Auth::user()->role->name == 'Client Member') {
        return redirect()->route('client.dashboard');
    } else {
        // Regular team member dashboard logic
        // Same task grouping logic as admin dashboard
        // Shows only assigned projects and tasks
    }
}</code></pre>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-shield-alt me-2"></i>SuperAdmin Middleware</h4>
          <pre><code class="language-php">class SuperAdminPermissionMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        $roleName = $user->role?->name;
        $isSuperOrAdmin = in_array($roleName, ['SuperAdmin', 'Admin']);
        $adminAccessLevelId = AccessLevel::where('name', 'Admin')->value('id');

        if ($isSuperOrAdmin || $user->access_level_id === $adminAccessLevelId) {
            return $next($request);
        }

        abort(403, 'Access denied. Only Super Admins or Admins are allowed.');
    }
}</code></pre>
        </div>
      </div>
    `,
    subsections: [
      { id: 'role-hierarchy', title: 'Role Hierarchy' },
      { id: 'dashboard-variations', title: 'Dashboard Variations' },
      { id: 'access-control', title: 'Access Control Implementation' },
      { id: 'permission-matrix', title: 'Permission Matrix' }
    ]
  },

  projectManagement: {
    id: 'project-management',
    title: 'Project Management Lifecycle',
    description: 'Complete project lifecycle management system',
    icon: 'fas fa-project-diagram',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-project-diagram me-3"></i>Project Management Lifecycle</h1>

        <div class="info-card">
          <p>The project creation process in ShieldsGF Portal follows a structured approach that ensures all necessary information is captured and proper relationships are established.</p>
        </div>

        <h2>Project Creation Workflow</h2>

        <div class="warning-card">
          <h4><i class="fas fa-exclamation-triangle me-2"></i>Prerequisites</h4>
          <ul>
            <li><strong>Client Must Exist:</strong> Every project requires an associated client</li>
            <li><strong>Brand Association (Optional):</strong> Clients may have associated brands/enterprises</li>
            <li><strong>User Permissions:</strong> Only Admins, SuperAdmins, or Team Members with Admin access can create projects</li>
          </ul>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-form me-2"></i>Required Project Fields</h4>
          <div class="row">
            <div class="col-md-6">
              <ul>
                <li><strong>Project Title:</strong> Required, Unique</li>
                <li><strong>Job Code:</strong> Required, Format: SP-XXX-YY</li>
                <li><strong>Client:</strong> Required, Dropdown Selection</li>
                <li><strong>Timeline:</strong> Required, Predefined Options</li>
              </ul>
            </div>
            <div class="col-md-6">
              <ul>
                <li><strong>Team Members:</strong> Required, Multi-select</li>
                <li><strong>Invoice Schedule:</strong> Required, Billing Frequency</li>
                <li><strong>Harvest Link:</strong> Optional, Time Tracking Integration</li>
                <li><strong>Social Details:</strong> Required, Array of Social Media Links</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Project Phases System</h2>

        <div class="info-card">
          <p>Projects in ShieldsGF Portal follow a standardized phase structure that provides clear milestones and progress tracking.</p>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-pencil-ruler me-2"></i>1. Design Phase</h4>
              <ul>
                <li>Initial planning and wireframing</li>
                <li>Client requirements gathering</li>
                <li>Design mockups and prototypes</li>
                <li>Design approval and finalization</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-code me-2"></i>2. Development Phase</h4>
              <ul>
                <li>Code implementation</li>
                <li>Feature development</li>
                <li>Integration and testing</li>
                <li>Code review and optimization</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-rocket me-2"></i>3. Deploy Phase</h4>
              <ul>
                <li>Testing and quality assurance</li>
                <li>Staging environment setup</li>
                <li>Production deployment</li>
                <li>Launch coordination</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-cogs me-2"></i>4. Manage Phase</h4>
              <ul>
                <li>Post-launch monitoring</li>
                <li>Bug fixes and optimizations</li>
                <li>Performance monitoring</li>
                <li>Client training and handover</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="info-card success-card">
          <h4><i class="fas fa-star me-2"></i>5. Voyager Phase</h4>
          <ul>
            <li>Long-term maintenance</li>
            <li>Ongoing support</li>
            <li>Feature enhancements</li>
            <li>Performance optimization</li>
          </ul>
        </div>

        <h2>Phase Management Features</h2>

        <div class="info-card">
          <ul>
            <li><strong>Category-Based Organization:</strong> Projects within phases are organized by categories</li>
            <li><strong>Target Date Tracking:</strong> Each phase has specific completion targets</li>
            <li><strong>Progress Visualization:</strong> Visual indicators show phase completion percentages</li>
            <li><strong>Automatic Transitions:</strong> Projects can be moved between phases via drag-and-drop</li>
            <li><strong>Completion Calculation:</strong> Project completion determined by highest phase ID target date</li>
          </ul>
        </div>

        <h2>Project Information Display</h2>

        <div class="row">
          <div class="col-md-4">
            <div class="info-card">
              <h4><i class="fas fa-tag me-2"></i>Job Code Format</h4>
              <ul>
                <li><strong>Structure:</strong> [CLIENT-PROJECT-YEAR]</li>
                <li><strong>Example:</strong> SP-003-25</li>
                <li><strong>Purpose:</strong> Unique identification</li>
                <li><strong>Usage:</strong> Displayed in all interfaces</li>
              </ul>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-card">
              <h4><i class="fas fa-chart-line me-2"></i>Phase Indicators</h4>
              <ul>
                <li><strong>Format:</strong> "PHASE X/Y"</li>
                <li><strong>Example:</strong> "PHASE 2/5"</li>
                <li><strong>Visual Elements:</strong> Progress bars</li>
                <li><strong>Color Coding:</strong> Different phase colors</li>
              </ul>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-card">
              <h4><i class="fas fa-users me-2"></i>Team Visualization</h4>
              <ul>
                <li><strong>Color-Coded Flags:</strong> Unique colors</li>
                <li><strong>Team Indicators:</strong> Visual flags</li>
                <li><strong>Role Identification:</strong> Different indicators</li>
                <li><strong>Hover Details:</strong> Names and roles</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    `,
    subsections: [
      { id: 'project-creation', title: 'Project Creation Workflow' },
      { id: 'project-phases', title: 'Project Phases System' },
      { id: 'phase-management', title: 'Phase Management Features' },
      { id: 'project-display', title: 'Project Information Display' }
    ]
  },

  taskManagement: {
    id: 'task-management',
    title: 'Task Management System',
    description: 'Comprehensive task tracking and collaboration system',
    icon: 'fas fa-tasks',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-tasks me-3"></i>Task Management System</h1>

        <div class="info-card">
          <p>The task management system provides comprehensive tracking and organization capabilities with role-based access control and real-time collaboration features.</p>
        </div>

        <h2>Task Categories and Workflow</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card danger-card">
              <h4><i class="fas fa-star me-2"></i>Urgent Tasks</h4>
              <ul>
                <li>High priority items requiring immediate attention</li>
                <li>Displayed with star icons for visual prominence</li>
                <li>Appear at top of task lists</li>
                <li>Generate automatic notifications</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card warning-card">
              <h4><i class="fas fa-exclamation me-2"></i>New Tasks</h4>
              <ul>
                <li>Recently created tasks awaiting assignment</li>
                <li>Special visual indicators for identification</li>
                <li>Require initial review and prioritization</li>
                <li>Auto-assigned based on project team</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-list me-2"></i>Regular Tasks</h4>
              <ul>
                <li>Standard workflow tasks</li>
                <li>Simple display without special icons</li>
                <li>Follow normal priority and due date rules</li>
                <li>Standard notification schedule</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-check-circle me-2"></i>Completed Tasks</h4>
              <ul>
                <li>Finished tasks awaiting final review</li>
                <li>Archive after confirmation</li>
                <li>Performance metrics tracking</li>
                <li>Client notification if required</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Rich Text Comment System</h2>

        <div class="info-card">
          <h4><i class="fas fa-comments me-2"></i>Advanced Comment Features</h4>
          <div class="row">
            <div class="col-md-6">
              <h5>Formatting Options:</h5>
              <ul>
                <li><strong>Bold Text:</strong> Ctrl+B or button toggle</li>
                <li><strong>Italic Text:</strong> Ctrl+I or button toggle</li>
                <li><strong>Underline Text:</strong> Ctrl+U or button toggle</li>
                <li><strong>Line Breaks:</strong> Shift+Enter for multi-line</li>
                <li><strong>Visual Feedback:</strong> Selected text highlighted</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h5>User Mention System:</h5>
              <ul>
                <li><strong>Trigger:</strong> @ symbol followed by user name</li>
                <li><strong>Dropdown:</strong> Real-time user search</li>
                <li><strong>Smart Replacement:</strong> Replaces partial typed name</li>
                <li><strong>Notifications:</strong> Mentioned users get alerts</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-bolt me-2"></i>Real-Time Features with Laravel Reverb</h4>
          <ul>
            <li><strong>Live Comments:</strong> Comments appear instantly for all users viewing the task</li>
            <li><strong>User Information:</strong> Real-time display of commenter name and profile image</li>
            <li><strong>Attachment Support:</strong> Live updates for file attachments in comments</li>
            <li><strong>Timezone Handling:</strong> Consistent timestamp display across different timezones</li>
            <li><strong>Notification Integration:</strong> Automatic email notifications for mentioned users</li>
          </ul>
        </div>

        <h2>Task Creation and Assignment</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-plus me-2"></i>Mandatory Fields</h4>
              <ul>
                <li><strong>Task Name:</strong> Descriptive title</li>
                <li><strong>Description:</strong> Detailed requirements</li>
                <li><strong>Project Assignment:</strong> Must be associated with project</li>
                <li><strong>Status:</strong> Initial status (typically "New")</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-cog me-2"></i>Optional Fields</h4>
              <ul>
                <li><strong>Due Date:</strong> Target completion date</li>
                <li><strong>Priority Level:</strong> Urgent, High, Medium, Low</li>
                <li><strong>Estimated Duration:</strong> Time estimate</li>
                <li><strong>Category:</strong> Task categorization</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-user-plus me-2"></i>Assignment Process</h4>
          <ul>
            <li><strong>Multi-User Assignment:</strong> Tasks can be assigned to multiple team members</li>
            <li><strong>Role-Based Assignment:</strong> Consider user roles and capabilities</li>
            <li><strong>Workload Balancing:</strong> System suggests assignments based on current workload</li>
            <li><strong>Notification System:</strong> Automatic notifications to assigned users</li>
          </ul>
        </div>

        <h2>Access Control</h2>

        <div class="info-card">
          <h4><i class="fas fa-shield-alt me-2"></i>Task Visibility Rules</h4>
          <ul>
            <li><strong>Admin/SuperAdmin:</strong> View all tasks across projects</li>
            <li><strong>Regular Users:</strong> Only assigned tasks visible</li>
            <li><strong>Project-Specific Views:</strong> Tasks filtered by project</li>
            <li><strong>Client Access:</strong> Limited visibility to relevant project tasks</li>
          </ul>
        </div>
      </div>
    `,
    subsections: [
      { id: 'task-categories', title: 'Task Categories and Workflow' },
      { id: 'comment-system', title: 'Rich Text Comment System' },
      { id: 'task-creation', title: 'Task Creation and Assignment' },
      { id: 'task-access-control', title: 'Access Control' }
    ]
  },

  statusHub: {
    id: 'status-hub',
    title: 'Status Hub - Visual Project Tracking',
    description: 'Central command center for visual project management',
    icon: 'fas fa-chart-bar',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-chart-bar me-3"></i>Status Hub - Visual Project Tracking</h1>

        <div class="info-card">
          <p>The Status Hub is the central command center for visual project management, providing a comprehensive overview of all projects across different phases with intuitive drag-and-drop functionality.</p>
        </div>

        <h2>Visual Project Representation</h2>

        <div class="dashboard-mockup">
          <pre>┌─────────────────────────────────────────────────────────────┐
│ 📊 Status Hub - Project Overview                           │
├─────────────────────────────────────────────────────────────┤
│ 📋 Design    │ 💻 Development │ 🚀 Deploy    │ 🔧 Manage   │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────┐  │ ┌─────────┐    │ ┌─────────┐  │ ┌─────────┐ │
│ │[SP-001] │  │ │[SP-003] │    │ │[SP-005] │  │ │[SP-007] │ │
│ │Project A│  │ │Project C│    │ │Project E│  │ │Project G│ │
│ │●●●○○ 60%│  │ │●●●●○ 80%│    │ │●●●●● 100%│ │ │●●●●● 100%│ │
│ │👤👤👤   │  │ │👤👤     │    │ │👤👤👤   │  │ │👤👤     │ │
│ └─────────┘  │ └─────────┘    │ └─────────┘  │ └─────────┘ │
│              │                │              │             │
│ ┌─────────┐  │ ┌─────────┐    │              │ ┌─────────┐ │
│ │[SP-002] │  │ │[SP-004] │    │              │ │[SP-008] │ │
│ │Project B│  │ │Project D│    │              │ │Project H│ │
│ │●●○○○ 40%│  │ │●●●○○ 60%│    │              │ │●●●●● 100%│ │
│ │👤👤     │  │ │👤👤👤   │    │              │ │👤       │ │
│ └─────────┘  │ └─────────┘    │              │ └─────────┘ │
└─────────────────────────────────────────────────────────────┘</pre>
        </div>

        <h2>Project Box Components</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-chart-pie me-2"></i>Progress Indicators</h4>
              <ul>
                <li><strong>Circular Progress Bars:</strong> Visual percentage completion</li>
                <li><strong>Color-Coded Phases:</strong> Different colors for each phase type</li>
                <li><strong>Percentage Display:</strong> Numerical progress indication</li>
                <li><strong>Phase Completion Status:</strong> Visual indicators for milestones</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-info-circle me-2"></i>Project Information</h4>
              <ul>
                <li><strong>Job Code:</strong> Unique project identifier (e.g., [SP-003-25])</li>
                <li><strong>Project Name:</strong> Descriptive project title</li>
                <li><strong>Client Information:</strong> Associated client details</li>
                <li><strong>Phase Indicator:</strong> Current phase and total phases</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-users me-2"></i>Team Member Visualization</h4>
          <ul>
            <li><strong>Color-Coded Flags:</strong> Each team member represented by unique color</li>
            <li><strong>Team Size Indicator:</strong> Visual representation of team size</li>
            <li><strong>Role Identification:</strong> Different visual indicators for different roles</li>
            <li><strong>Hover Details:</strong> Team member names and roles on hover</li>
          </ul>
        </div>

        <h2>Interactive Features</h2>

        <div class="info-card">
          <h4><i class="fas fa-hand-rock me-2"></i>Drag-and-Drop Functionality</h4>
          <ul>
            <li><strong>Phase Transitions:</strong> Drag projects between phase columns</li>
            <li><strong>Real-Time Updates:</strong> Immediate visual feedback during drag operations</li>
            <li><strong>Validation:</strong> System prevents invalid phase transitions</li>
            <li><strong>Auto-Save:</strong> Changes saved automatically upon drop</li>
            <li><strong>Notification System:</strong> Team notifications for phase changes</li>
          </ul>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-mouse-pointer me-2"></i>Project Actions</h4>
          <ul>
            <li><strong>Click to View:</strong> Click project boxes for detailed project view</li>
            <li><strong>Archive/Unarchive:</strong> Toggle project visibility</li>
            <li><strong>Edit Project:</strong> Direct access to project editing</li>
            <li><strong>Team Management:</strong> Quick team member assignment</li>
          </ul>
        </div>

        <h2>Category-Based Organization</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-tags me-2"></i>Phase Categories</h4>
              <ul>
                <li><strong>Design Categories:</strong> UI/UX, Wireframing, Prototyping</li>
                <li><strong>Development Categories:</strong> Frontend, Backend, Integration</li>
                <li><strong>Deploy Categories:</strong> Testing, Staging, Production</li>
                <li><strong>Manage Categories:</strong> Maintenance, Support, Optimization</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-palette me-2"></i>Category Display</h4>
              <ul>
                <li><strong>Category Names:</strong> Displayed instead of generic phase names</li>
                <li><strong>Color Coding:</strong> Consistent color scheme across categories</li>
                <li><strong>Progress Tracking:</strong> Category-specific progress indicators</li>
                <li><strong>Filtering Options:</strong> Filter by category or phase type</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Archive Management</h2>

        <div class="info-card">
          <h4><i class="fas fa-archive me-2"></i>Archive System</h4>
          <ul>
            <li><strong>Hide Completed Projects:</strong> Remove clutter from active view</li>
            <li><strong>Restore Projects:</strong> Bring archived projects back to active status</li>
            <li><strong>Archive History:</strong> Track when projects were archived</li>
            <li><strong>Bulk Operations:</strong> Archive multiple projects simultaneously</li>
            <li><strong>Permission Control:</strong> Only authorized users can archive/restore</li>
          </ul>
        </div>
      </div>
    `,
    subsections: [
      { id: 'visual-representation', title: 'Visual Project Representation' },
      { id: 'project-components', title: 'Project Box Components' },
      { id: 'interactive-features', title: 'Interactive Features' },
      { id: 'category-organization', title: 'Category-Based Organization' }
    ]
  },

  ptoCalendar: {
    id: 'pto-calendar',
    title: 'PTO Calendar & Holiday Management',
    description: 'Comprehensive time-off and holiday management system',
    icon: 'fas fa-calendar-alt',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-calendar-alt me-3"></i>PTO Calendar & Holiday Management</h1>

        <div class="info-card">
          <p>The PTO Calendar system provides comprehensive holiday and time-off management with role-based permissions and regional customization.</p>
        </div>

        <h2>Admin Capabilities</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card warning-card">
              <h4><i class="fas fa-globe me-2"></i>Global Holiday Management</h4>
              <ul>
                <li><strong>Company-Wide Holidays:</strong> Add holidays that apply to all users</li>
                <li><strong>Regional Settings:</strong> Choose between Indian holidays, US holidays, or custom</li>
                <li><strong>Holiday Types:</strong> National holidays, company events, office closures</li>
                <li><strong>Bulk Import:</strong> Import standard holiday calendars</li>
                <li><strong>Recurring Events:</strong> Set up annual recurring holidays</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card warning-card">
              <h4><i class="fas fa-user-clock me-2"></i>Personal Leave Administration</h4>
              <ul>
                <li><strong>Add Personal Leave:</strong> Admins can add PTO for individual team members</li>
                <li><strong>Leave Approval:</strong> Approve or deny PTO requests</li>
                <li><strong>Leave Balance Tracking:</strong> Monitor team member PTO balances</li>
                <li><strong>Leave Policies:</strong> Configure company leave policies</li>
                <li><strong>Reporting:</strong> Generate leave reports and analytics</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="info-card warning-card">
          <h4><i class="fas fa-users-cog me-2"></i>Team PTO Oversight</h4>
          <ul>
            <li><strong>Team Calendar View:</strong> See all team member PTO in one view</li>
            <li><strong>Conflict Detection:</strong> Identify scheduling conflicts</li>
            <li><strong>Coverage Planning:</strong> Ensure adequate team coverage</li>
            <li><strong>Notification Management:</strong> Configure PTO notification settings</li>
          </ul>
        </div>

        <h2>User Capabilities</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-calendar-plus me-2"></i>Personal PTO Management</h4>
              <ul>
                <li><strong>PTO Requests:</strong> Submit time-off requests for approval</li>
                <li><strong>Personal Calendar:</strong> View personal PTO and holidays</li>
                <li><strong>Balance Tracking:</strong> Monitor available PTO balance</li>
                <li><strong>Request History:</strong> View past PTO requests and approvals</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card success-card">
              <h4><i class="fas fa-eye me-2"></i>Team Visibility</h4>
              <ul>
                <li><strong>Team Calendar:</strong> View team member PTO (with privacy settings)</li>
                <li><strong>Project Impact:</strong> See how PTO affects project timelines</li>
                <li><strong>Coverage Information:</strong> Identify who's covering responsibilities</li>
                <li><strong>Holiday Awareness:</strong> Stay informed about company holidays</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Interactive Calendar Interface</h2>

        <div class="dashboard-mockup">
          <pre>┌─────────────────────────────────────────────────────────────┐
│ 📅 PTO Calendar - December 2024                            │
├─────────────────────────────────────────────────────────────┤
│ Sun │ Mon │ Tue │ Wed │ Thu │ Fri │ Sat                     │
├─────────────────────────────────────────────────────────────┤
│  1  │  2  │  3  │  4  │  5  │  6  │  7                      │
│     │     │     │     │     │     │                         │
├─────────────────────────────────────────────────────────────┤
│  8  │  9  │ 10  │ 11  │ 12  │ 13  │ 14                     │
│     │     │     │     │     │     │                         │
├─────────────────────────────────────────────────────────────┤
│ 15  │ 16  │ 17  │ 18  │ 19  │ 20  │ 21                     │
│     │     │     │     │     │     │                         │
├─────────────────────────────────────────────────────────────┤
│ 22  │ 23  │ 24  │ 25  │ 26  │ 27  │ 28                     │
│     │     │     │🎄   │🏖️   │     │                         │
│     │     │     │Xmas │John │     │                         │
│     │     │     │     │Out  │     │                         │
├─────────────────────────────────────────────────────────────┤
│ 29  │ 30  │ 31  │     │     │     │                         │
│     │     │🎊   │     │     │     │                         │
│     │     │NYE  │     │     │     │                         │
└─────────────────────────────────────────────────────────────┘</pre>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-calendar-check me-2"></i>Event Types and Display</h4>
          <ul>
            <li><strong>🎄 Company Holidays:</strong> Global holidays (Christmas, New Year, etc.)</li>
            <li><strong>🏖️ Personal PTO:</strong> Individual time off ("John is out")</li>
            <li><strong>📅 Project Milestones:</strong> Project completion dates (blue)</li>
            <li><strong>🎯 Phase Deadlines:</strong> Phase completion dates (color-coded)</li>
          </ul>
        </div>

        <h2>AJAX-Based Operations</h2>

        <div class="info-card">
          <h4><i class="fas fa-bolt me-2"></i>Real-Time Calendar Updates</h4>
          <ul>
            <li><strong>Real-Time Updates:</strong> Add events without page refresh</li>
            <li><strong>Instant Feedback:</strong> Immediate visual confirmation</li>
            <li><strong>Error Handling:</strong> Graceful error messages for conflicts</li>
            <li><strong>Auto-Save:</strong> Changes saved automatically</li>
          </ul>
        </div>

        <h2>Integration Features</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-chart-pie me-2"></i>Resource Allocation Integration</h4>
              <ul>
                <li><strong>PTO Impact:</strong> Automatically factors PTO into resource calculations</li>
                <li><strong>Availability Tracking:</strong> Updates team member availability</li>
                <li><strong>Project Timeline Adjustment:</strong> Adjusts project timelines for PTO</li>
                <li><strong>Workload Redistribution:</strong> Suggests workload adjustments</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-project-diagram me-2"></i>Project Timeline Integration</h4>
              <ul>
                <li><strong>Milestone Visibility:</strong> Project completion dates shown in blue</li>
                <li><strong>Phase Deadlines:</strong> Phase completion dates with color coding</li>
                <li><strong>Conflict Alerts:</strong> Warnings for PTO conflicts with deadlines</li>
                <li><strong>Timeline Adjustments:</strong> Automatic timeline adjustments for holidays</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    `,
    subsections: [
      { id: 'admin-capabilities', title: 'Admin Capabilities' },
      { id: 'user-capabilities', title: 'User Capabilities' },
      { id: 'calendar-interface', title: 'Interactive Calendar Interface' },
      { id: 'integration-features', title: 'Integration Features' }
    ]
  },

  resourceAllocation: {
    id: 'resource-allocation',
    title: 'Resource Allocation & Utilization Tracking',
    description: 'Advanced weekly resource planning and team utilization system',
    icon: 'fas fa-chart-pie',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-chart-pie me-3"></i>Resource Allocation & Utilization Tracking</h1>

        <div class="info-card">
          <p>The Resource Allocation system provides sophisticated weekly resource planning and utilization tracking, accounting for different user roles, project phases, and time-off schedules.</p>
        </div>

        <h2>Core Concepts</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-clock me-2"></i>Work Week Standards</h4>
              <ul>
                <li><strong>Standard Work Week:</strong> 32 hours (6+ hours per day)</li>
                <li><strong>Full-Time Equivalent:</strong> 40 hours for percentage calculations</li>
                <li><strong>Overtime Tracking:</strong> Allocation can exceed 100%</li>
                <li><strong>Flexible Scheduling:</strong> Accommodates different work patterns</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-users-cog me-2"></i>Multi-Role System</h4>
              <ul>
                <li><strong>Developer Role:</strong> Code implementation and technical tasks</li>
                <li><strong>Designer Role:</strong> UI/UX design and creative work</li>
                <li><strong>Project Manager Role:</strong> 20-30% allocation during active phases</li>
                <li><strong>Customer Success Role:</strong> Client communication and support</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Calendar-Based View</h2>

        <div class="dashboard-mockup">
          <pre>┌─────────────────────────────────────────────────────────────┐
│ 📊 Resource Allocation - December 2024                     │
├─────────────────────────────────────────────────────────────┤
│ Week of Dec 2-8, 2024                                      │
├─────────────────────────────────────────────────────────────┤
│ 👨‍💻 John Developer: 85% (27/32 hrs)                          │
│   • SP-001 Design Phase: 40% (13 hrs)                     │
│   • SP-003 Development: 45% (14 hrs)                      │
├─────────────────────────────────────────────────────────────┤
│ 🎨 Sarah Designer: 110% (35/32 hrs) ⚠️                     │
│   • SP-001 Design Phase: 60% (19 hrs)                     │
│   • SP-002 Design Phase: 50% (16 hrs)                     │
├─────────────────────────────────────────────────────────────┤
│ 📋 Mike PM: 75% (24/32 hrs)                               │
│   • SP-001 Management: 25% (8 hrs)                        │
│   • SP-003 Management: 25% (8 hrs)                        │
│   • SP-004 Management: 25% (8 hrs)                        │
├─────────────────────────────────────────────────────────────┤
│ 🏖️ Holiday: Christmas Day (Dec 25)                         │
└─────────────────────────────────────────────────────────────┘</pre>
        </div>

        <h2>Utilization Calculation Logic</h2>

        <div class="info-card">
          <h4><i class="fas fa-calculator me-2"></i>Weekly Utilization Formula</h4>
          <pre><code class="language-javascript">// Base calculation for each team member
const weeklyUtilization = {
    availableHours: userRole.hoursPerWeek || 32,
    allocatedHours: 0,
    projects: []
};

// For each project phase the user is involved in
projects.forEach(project => {
    const phaseHours = calculatePhaseHours(project, user, week);
    weeklyUtilization.allocatedHours += phaseHours;
    weeklyUtilization.projects.push({
        projectCode: project.job_code,
        phaseName: project.currentPhase.name,
        hours: phaseHours,
        percentage: (phaseHours / weeklyUtilization.availableHours) * 100
    });
});

// Calculate total utilization percentage
const utilizationPercentage =
    (weeklyUtilization.allocatedHours / weeklyUtilization.availableHours) * 100;</code></pre>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-chart-line me-2"></i>Project Manager Allocation Rules</h4>
          <ul>
            <li><strong>Design Phase:</strong> 20-30% PM time allocation</li>
            <li><strong>Development Phase:</strong> 20-30% PM time allocation</li>
            <li><strong>Deploy Phase:</strong> 20-30% PM time allocation</li>
            <li><strong>Manage Phase:</strong> 20-30% PM time allocation</li>
            <li><strong>Voyager Phase:</strong> Reduced PM allocation (10-15%)</li>
          </ul>
        </div>

        <h2>Advanced Features</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-calendar-week me-2"></i>Extended Timeline Planning</h4>
              <ul>
                <li><strong>Beyond Current Month:</strong> Shows weeks until all projects complete</li>
                <li><strong>Long-Term Planning:</strong> Resource planning for future phases</li>
                <li><strong>Phase Transition Tracking:</strong> Resources move between teams as phases complete</li>
                <li><strong>Capacity Planning:</strong> Identify future resource needs</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-exclamation-triangle me-2"></i>Overallocation Management</h4>
              <ul>
                <li><strong>Over 100% Allocation:</strong> System allows and tracks overallocation</li>
                <li><strong>Warning Indicators:</strong> Visual alerts for overallocated team members</li>
                <li><strong>Rebalancing Suggestions:</strong> System suggests workload adjustments</li>
                <li><strong>Burnout Prevention:</strong> Monitoring for sustainable workloads</li>
              </ul>
            </div>
          </div>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-database me-2"></i>Resources Table Integration</h4>
          <ul>
            <li><strong>Role-Based Hours:</strong> Different hour allocations per role (Developer, Designer, PM, CS)</li>
            <li><strong>User Flexibility:</strong> Users can have multiple roles with different hour commitments</li>
            <li><strong>Dynamic Allocation:</strong> Hours adjust based on role assignments</li>
            <li><strong>Historical Tracking:</strong> Track changes in role assignments over time</li>
          </ul>
        </div>

        <h2>Holiday and PTO Integration</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card warning-card">
              <h4><i class="fas fa-calendar-times me-2"></i>Leave Time Accounting</h4>
              <ul>
                <li><strong>Personal PTO:</strong> Reduces available hours for affected weeks</li>
                <li><strong>Company Holidays:</strong> Affects entire team availability</li>
                <li><strong>Partial Week PTO:</strong> Proportional hour reduction</li>
                <li><strong>Advanced Notice:</strong> Future PTO affects future resource planning</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card warning-card">
              <h4><i class="fas fa-adjust me-2"></i>Automatic Adjustments</h4>
              <ul>
                <li><strong>Holiday Weeks:</strong> Reduced available hours automatically calculated</li>
                <li><strong>PTO Impact:</strong> Individual availability adjustments</li>
                <li><strong>Project Timeline Shifts:</strong> Automatic timeline adjustments</li>
                <li><strong>Team Coverage:</strong> Identifies coverage needs during PTO</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Display and Visualization</h2>

        <div class="info-card">
          <h4><i class="fas fa-eye me-2"></i>Enhanced Event Display</h4>
          <ul>
            <li><strong>Reduced Height Events:</strong> More events visible simultaneously</li>
            <li><strong>Full Project Names:</strong> No truncation of project names</li>
            <li><strong>Clickable Details:</strong> Click for complete project information</li>
            <li><strong>Team Role Indicators:</strong> Visual indicators for different team roles</li>
            <li><strong>Holiday Positioning:</strong> Holiday events appear at end of display</li>
          </ul>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-chart-bar me-2"></i>Utilization Metrics</h4>
          <ul>
            <li><strong>Percentage Display:</strong> Clear utilization percentages</li>
            <li><strong>Hour Breakdown:</strong> Format: (allocated/available) hours</li>
            <li><strong>Color Coding:</strong> Green (under-utilized), Yellow (optimal), Red (over-allocated)</li>
            <li><strong>Trend Analysis:</strong> Week-over-week utilization trends</li>
          </ul>
        </div>

        <h2>Voyager Project Handling</h2>

        <div class="info-card success-card">
          <h4><i class="fas fa-rocket me-2"></i>Long-Term Project Management</h4>
          <ul>
            <li><strong>Post-Deploy Display:</strong> Voyager projects shown after Deploy and Manage phases</li>
            <li><strong>Maintenance Allocation:</strong> Reduced resource allocation for ongoing maintenance</li>
            <li><strong>Flexible Scheduling:</strong> More flexible time allocation for maintenance tasks</li>
            <li><strong>Client Relationship:</strong> Ongoing client relationship management</li>
          </ul>
        </div>
      </div>
    `,
    subsections: [
      { id: 'core-concepts', title: 'Core Concepts' },
      { id: 'calendar-view', title: 'Calendar-Based View' },
      { id: 'utilization-calculation', title: 'Utilization Calculation Logic' },
      { id: 'advanced-features', title: 'Advanced Features' },
      { id: 'holiday-integration', title: 'Holiday and PTO Integration' }
    ]
  },

  architecture: {
    id: 'architecture',
    title: 'System Architecture & Technology Stack',
    description: 'Comprehensive technical architecture overview',
    icon: 'fas fa-sitemap',
    content: `
      <div class="content-section">
        <h1><i class="fas fa-sitemap me-3"></i>System Architecture & Technology Stack</h1>

        <div class="info-card">
          <p>ShieldsGF Portal is built on a modern, scalable architecture using Laravel as the foundation with advanced real-time capabilities and responsive frontend technologies.</p>
        </div>

        <h2>Core Technology Stack</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-server me-2"></i>Backend Technologies</h4>
              <ul>
                <li><strong>Laravel 12.x:</strong> PHP 8.2+ framework for robust backend</li>
                <li><strong>Livewire 3.6+:</strong> Reactive components for dynamic interfaces</li>
                <li><strong>Laravel Reverb 1.5+:</strong> WebSocket server for real-time features</li>
                <li><strong>MySQL 8.0+:</strong> Optimized relational database</li>
                <li><strong>Spatie Laravel Permission:</strong> Role and permission management</li>
                <li><strong>Spatie Laravel PDF:</strong> PDF generation and reporting</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-desktop me-2"></i>Frontend Technologies</h4>
              <ul>
                <li><strong>Bootstrap 5.3.3:</strong> Responsive CSS framework</li>
                <li><strong>jQuery 3.6.4:</strong> JavaScript library for DOM manipulation</li>
                <li><strong>Vite:</strong> Modern asset bundling and hot module replacement</li>
                <li><strong>Laravel Echo:</strong> WebSocket client for real-time updates</li>
                <li><strong>Pusher.js:</strong> Real-time communication integration</li>
                <li><strong>Custom CSS:</strong> Tailored styling with CSS custom properties</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Architecture Patterns</h2>

        <div class="info-card">
          <h4><i class="fas fa-layer-group me-2"></i>MVC Architecture with Livewire Components</h4>
          <pre><code class="language-php">// Traditional Laravel Controller
class ProjectController extends Controller
{
    public function index()
    {
        return view('projects.index', [
            'projects' => Project::with('client', 'users')->get()
        ]);
    }
}

// Livewire Component for Dynamic Interactions
class ProjectList extends Component
{
    public $projects;
    public $search = '';

    public function render()
    {
        $this->projects = Project::where('name', 'like', '%'.$this->search.'%')
            ->with('client', 'users')
            ->get();

        return view('livewire.project-list');
    }

    public function updateProject($projectId, $status)
    {
        $project = Project::find($projectId);
        $project->update(['status' => $status]);

        // Real-time broadcast
        broadcast(new ProjectUpdated($project));
    }
}</code></pre>
        </div>

        <h2>Real-Time Architecture with Laravel Reverb</h2>

        <div class="info-card">
          <h4><i class="fas fa-bolt me-2"></i>WebSocket Implementation</h4>
          <pre><code class="language-php">// Broadcasting Configuration
'reverb' => [
    'driver' => 'reverb',
    'key' => env('REVERB_APP_KEY'),
    'secret' => env('REVERB_APP_SECRET'),
    'app_id' => env('REVERB_APP_ID'),
    'options' => [
        'host' => env('REVERB_HOST', '127.0.0.1'),
        'port' => env('REVERB_PORT', 8080),
        'scheme' => env('REVERB_SCHEME', 'http'),
    ],
],

// Event Broadcasting
class CommentAdded implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $comment;
    public $task;

    public function __construct(Comment $comment, Task $task)
    {
        $this->comment = $comment;
        $this->task = $task;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('task.' . $this->task->id);
    }
}</code></pre>
        </div>

        <div class="info-card">
          <h4><i class="fas fa-comments me-2"></i>Frontend WebSocket Integration</h4>
          <pre><code class="language-javascript">// Laravel Echo Configuration
window.Echo = new Echo({
    broadcaster: 'reverb',
    key: import.meta.env.VITE_REVERB_APP_KEY,
    wsHost: import.meta.env.VITE_REVERB_HOST,
    wsPort: import.meta.env.VITE_REVERB_PORT,
    wssPort: import.meta.env.VITE_REVERB_PORT,
    forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
    enabledTransports: ['ws', 'wss'],
});

// Real-time Comment Updates
Echo.private('task.' + taskId)
    .listen('CommentAdded', (e) => {
        const commentHtml = \`
            <div class="comment-item">
                <strong>\${e.comment.user.name}:</strong>
                <p>\${e.comment.content}</p>
                <small>\${e.comment.created_at}</small>
            </div>
        \`;
        $('#comments-container').append(commentHtml);
    });</code></pre>
        </div>

        <h2>Database Architecture</h2>

        <div class="info-card">
          <h4><i class="fas fa-database me-2"></i>Core Entity Relationships</h4>
          <pre><code class="language-sql">-- Users and Roles
users (id, name, email, role_id, access_level_id)
roles (id, name, permissions)
access_levels (id, name, description)

-- Projects and Clients
projects (id, name, job_code, client_id, status_id, created_at)
clients (id, name, email, brand_id)
brands (id, name, description)

-- Project Phases and Tasks
project_phases (id, project_id, phase_id, target_date, completed_at)
phases (id, name, order, color)
tasks (id, name, description, project_id, status_id, created_at)
task_user (task_id, user_id) -- Many-to-many pivot

-- Comments and Files
comments (id, task_id, user_id, content, created_at)
files (id, task_id, filename, path, size, mime_type)

-- Resource Allocation
resources (id, user_id, role, hours_per_week)
project_allocations (id, project_id, user_id, role, hours, week_start)</code></pre>
        </div>

        <h2>Security Architecture</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card danger-card">
              <h4><i class="fas fa-shield-alt me-2"></i>Authentication & Authorization</h4>
              <ul>
                <li><strong>Laravel Sanctum:</strong> API token authentication</li>
                <li><strong>Session-Based Auth:</strong> Web interface authentication</li>
                <li><strong>Role-Based Access:</strong> Spatie Laravel Permission integration</li>
                <li><strong>Middleware Protection:</strong> Route-level access control</li>
                <li><strong>CSRF Protection:</strong> Cross-site request forgery prevention</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card danger-card">
              <h4><i class="fas fa-lock me-2"></i>Data Protection</h4>
              <ul>
                <li><strong>Input Validation:</strong> Comprehensive form validation</li>
                <li><strong>SQL Injection Prevention:</strong> Eloquent ORM protection</li>
                <li><strong>XSS Protection:</strong> Output escaping and sanitization</li>
                <li><strong>File Upload Security:</strong> MIME type validation and storage isolation</li>
                <li><strong>Environment Security:</strong> Sensitive data in environment variables</li>
              </ul>
            </div>
          </div>
        </div>

        <h2>Performance Optimization</h2>

        <div class="info-card">
          <h4><i class="fas fa-tachometer-alt me-2"></i>Optimization Strategies</h4>
          <ul>
            <li><strong>Eager Loading:</strong> Prevent N+1 query problems with proper relationship loading</li>
            <li><strong>Database Indexing:</strong> Strategic indexes on frequently queried columns</li>
            <li><strong>Caching:</strong> Redis/Memcached for session and application caching</li>
            <li><strong>Asset Optimization:</strong> Vite for modern asset bundling and minification</li>
            <li><strong>Query Optimization:</strong> Optimized database queries with proper GROUP BY usage</li>
            <li><strong>Livewire Optimization:</strong> Efficient component updates and lazy loading</li>
          </ul>
        </div>

        <h2>Deployment Architecture</h2>

        <div class="row">
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-cloud me-2"></i>Production Environment</h4>
              <ul>
                <li><strong>Web Server:</strong> Nginx with PHP-FPM</li>
                <li><strong>Database:</strong> MySQL 8.0 with optimized configuration</li>
                <li><strong>WebSocket Server:</strong> Laravel Reverb with process management</li>
                <li><strong>Queue Workers:</strong> Laravel queues for background processing</li>
                <li><strong>File Storage:</strong> Local storage with S3 option</li>
              </ul>
            </div>
          </div>
          <div class="col-md-6">
            <div class="info-card">
              <h4><i class="fas fa-tools me-2"></i>Development Workflow</h4>
              <ul>
                <li><strong>Version Control:</strong> Git with feature branch workflow</li>
                <li><strong>Dependency Management:</strong> Composer for PHP, NPM for JavaScript</li>
                <li><strong>Asset Building:</strong> Vite for development and production builds</li>
                <li><strong>Database Migrations:</strong> Version-controlled schema changes</li>
                <li><strong>Environment Management:</strong> Separate configurations for dev/staging/production</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    `,
    subsections: [
      { id: 'technology-stack', title: 'Core Technology Stack' },
      { id: 'architecture-patterns', title: 'Architecture Patterns' },
      { id: 'real-time-architecture', title: 'Real-Time Architecture' },
      { id: 'database-architecture', title: 'Database Architecture' },
      { id: 'security-architecture', title: 'Security Architecture' }
    ]
  }
};

// Navigation structure for the documentation
const navigationStructure = [
  {
    title: 'Introduction & Overview',
    icon: 'fas fa-home',
    id: 'introduction',
    items: [
      { title: 'Overview', id: 'overview', icon: 'fas fa-info-circle' },
      { title: 'Key Features', id: 'key-features', icon: 'fas fa-star' },
      { title: 'Technology Stack', id: 'technology-stack', icon: 'fas fa-layer-group' },
      { title: 'Architecture', id: 'architecture', icon: 'fas fa-sitemap' }
    ]
  },
  {
    title: 'Getting Started',
    icon: 'fas fa-rocket',
    id: 'getting-started-section',
    items: [
      { title: 'System Requirements', id: 'getting-started', icon: 'fas fa-server' },
      { title: 'Installation Guide', id: 'installation', icon: 'fas fa-download' },
      { title: 'Configuration', id: 'configuration', icon: 'fas fa-cog' },
      { title: 'First Login', id: 'first-login', icon: 'fas fa-sign-in-alt' }
    ]
  },
  {
    title: 'User Roles & Access',
    icon: 'fas fa-users',
    id: 'user-roles',
    items: [
      { title: 'Role Hierarchy', id: 'role-hierarchy', icon: 'fas fa-sitemap' },
      { title: 'Permission Matrix', id: 'permission-matrix', icon: 'fas fa-table' },
      { title: 'Dashboard Variations', id: 'dashboard-variations', icon: 'fas fa-tachometer-alt' },
      { title: 'Access Levels', id: 'access-levels', icon: 'fas fa-key' }
    ]
  },
  {
    title: 'Core Features',
    icon: 'fas fa-cogs',
    id: 'core-features',
    items: [
      { title: 'Project Management', id: 'project-management', icon: 'fas fa-project-diagram' },
      { title: 'Task Management', id: 'task-management', icon: 'fas fa-tasks' },
      { title: 'Status Hub', id: 'status-hub', icon: 'fas fa-chart-bar' },
      { title: 'PTO Calendar', id: 'pto-calendar', icon: 'fas fa-calendar-alt' },
      { title: 'Resource Allocation', id: 'resource-allocation', icon: 'fas fa-chart-pie' },
      { title: 'Team Collaboration', id: 'team-collaboration', icon: 'fas fa-comments' },
      { title: 'Real-time Features', id: 'real-time-features', icon: 'fas fa-bolt' },
      { title: 'File Management', id: 'file-management', icon: 'fas fa-folder' }
    ]
  },
  {
    title: 'Admin Features',
    icon: 'fas fa-user-shield',
    id: 'admin-features',
    items: [
      { title: 'Admin Dashboard', id: 'admin-dashboard', icon: 'fas fa-tachometer-alt' },
      { title: 'User Management', id: 'user-management', icon: 'fas fa-users-cog' },
      { title: 'System Configuration', id: 'system-config', icon: 'fas fa-cog' },
      { title: 'Permissions', id: 'permissions', icon: 'fas fa-shield-alt' }
    ]
  },
  {
    title: 'Technical Documentation',
    icon: 'fas fa-code',
    id: 'technical',
    items: [
      { title: 'Architecture', id: 'architecture', icon: 'fas fa-sitemap' },
      { title: 'Database Schema', id: 'database', icon: 'fas fa-database' },
      { title: 'API Reference', id: 'api', icon: 'fas fa-plug' },
      { title: 'Security', id: 'security', icon: 'fas fa-lock' }
    ]
  }
];

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { docsData, navigationStructure };
}
